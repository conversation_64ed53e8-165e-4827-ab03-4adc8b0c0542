import { WEBUI_API_BASE_URL, WEBUI_BASE_URL, AUDIO_API_BASE_URL } from '@/lib/constants';
import type { ApiResponse, Model } from '@/lib/types';

// Base API client
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = WEBUI_API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (token) {
      headers.authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${url}`, error);
      throw error;
    }
  }

  async get<T>(endpoint: string, token?: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' }, token);
  }

  async post<T>(endpoint: string, data?: any, token?: string): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      },
      token
    );
  }

  async put<T>(endpoint: string, data?: any, token?: string): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      },
      token
    );
  }

  async delete<T>(endpoint: string, token?: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' }, token);
  }

  async patch<T>(endpoint: string, data?: any, token?: string): Promise<T> {
    return this.request<T>(
      endpoint,
      {
        method: 'PATCH',
        body: data ? JSON.stringify(data) : undefined,
      },
      token
    );
  }
}

// Create API client instance
export const apiClient = new ApiClient();

// Models API
export const getModels = async (
  token: string = '',
  connections: object | null = null,
  base: boolean = false
): Promise<Model[]> => {
  try {
    const endpoint = `/models${base ? '/base' : ''}`;
    const headers: HeadersInit = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    };
    
    // Always include Authorization header if token is provided
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    const response = await fetch(`${WEBUI_BASE_URL}/api${endpoint}`, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } else {
          const text = await response.text();
          if (text.includes('<!doctype') || text.includes('<html')) {
            errorMessage = `API returned HTML instead of JSON. Status: ${response.status}. This usually means the API endpoint is incorrect or requires authentication.`;
          } else {
            errorMessage = `API returned non-JSON response: ${text.substring(0, 200)}...`;
          }
        }
      } catch (parseError) {
        errorMessage = `Failed to parse error response. Status: ${response.status}`;
      }
      
      console.error('Models API error:', response.status, errorMessage);
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const text = await response.text();
      throw new Error(`API returned non-JSON response: ${text.substring(0, 200)}...`);
    }

    const res = await response.json();
    let models = res?.data ?? res ?? [];

    // Handle connections logic
    if (connections && !base) {
      let localModels: any[] = [];

      if (connections) {
        const OPENAI_API_BASE_URLS = (connections as any).OPENAI_API_BASE_URLS;
        const OPENAI_API_KEYS = (connections as any).OPENAI_API_KEYS;
        const OPENAI_API_CONFIGS = (connections as any).OPENAI_API_CONFIGS;

        if (OPENAI_API_BASE_URLS && OPENAI_API_KEYS && OPENAI_API_CONFIGS) {
          const requests = [];
          for (const idx in OPENAI_API_BASE_URLS) {
            const url = OPENAI_API_BASE_URLS[idx];

            if (idx.toString() in OPENAI_API_CONFIGS) {
              const apiConfig = OPENAI_API_CONFIGS[idx.toString()] ?? {};

              const enable = apiConfig?.enable ?? true;
              const modelIds = apiConfig?.model_ids ?? [];

              if (enable) {
                if (modelIds.length > 0) {
                  const modelList = {
                    object: 'list',
                    data: modelIds.map((modelId: string) => ({
                      id: modelId,
                      name: modelId,
                      owned_by: 'openai',
                      openai: { id: modelId },
                      urlIdx: idx
                    }))
                  };

                  requests.push(
                    (async () => {
                      return modelList;
                    })()
                  );
                } else {
                  // For now, we'll skip the direct OpenAI API call
                  // This would need getOpenAIModelsDirect implementation
                  requests.push(
                    (async () => {
                      return {
                        object: 'list',
                        data: [],
                        urlIdx: idx
                      };
                    })()
                  );
                }
              } else {
                requests.push(
                  (async () => {
                    return {
                      object: 'list',
                      data: [],
                      urlIdx: idx
                    };
                  })()
                );
              }
            }
          }

          const responses = await Promise.all(requests);

          for (const idx in responses) {
            const response = responses[idx];
            const apiConfig = OPENAI_API_CONFIGS[idx.toString()] ?? {};

            let responseModels = Array.isArray(response) ? response : (response?.data ?? []);
            responseModels = responseModels.map((model: any) => ({ ...model, openai: { id: model.id }, urlIdx: idx }));

            const prefixId = apiConfig.prefix_id;
            if (prefixId) {
              for (const model of responseModels) {
                model.id = `${prefixId}.${model.id}`;
              }
            }

            const tags = apiConfig.tags;
            if (tags) {
              for (const model of responseModels) {
                model.tags = tags;
              }
            }

            localModels = localModels.concat(responseModels);
          }
        }
      }

      models = models.concat(
        localModels.map((model) => ({
          ...model,
          name: model?.name ?? model?.id,
          direct: true
        }))
      );

      // Remove duplicates
      const modelsMap: Record<string, any> = {};
      for (const model of models) {
        modelsMap[model.id] = model;
      }

      models = Object.values(modelsMap);
    }

    return models;
  } catch (error) {
    console.error('Failed to fetch models:', error);
    
    // Return mock models if API fails (for development/testing)
    console.log('Using mock models for testing');
    return [
      {
        id: 'llama2:latest',
        name: 'Llama 2 Latest',
        owned_by: 'ollama',
        info: {
          meta: {
            description: 'Llama 2 7B model from Ollama',
            parameter_size: '7B',
            capabilities: ['text-generation']
          }
        }
      },
      {
        id: 'qwen2:7b', 
        name: 'Qwen2 7B',
        owned_by: 'ollama',
        info: {
          meta: {
            description: 'Qwen2 7B model from Ollama',
            parameter_size: '7B',
            capabilities: ['text-generation']
          }
        }
      }
    ];
  }
};

// Backend config API
export const getBackendConfig = async (): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/config`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const contentType = response.headers.get('content-type');
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } else {
          const text = await response.text();
          if (text.includes('<!doctype') || text.includes('<html')) {
            errorMessage = `Config API returned HTML instead of JSON. Status: ${response.status}. This usually means the API endpoint is incorrect.`;
          } else {
            errorMessage = `Config API returned non-JSON response: ${text.substring(0, 200)}...`;
          }
        }
      } catch (parseError) {
        errorMessage = `Failed to parse config error response. Status: ${response.status}`;
      }
      
      console.error('Backend config API error:', response.status, errorMessage);
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const text = await response.text();
      throw new Error(`Config API returned non-JSON response: ${text.substring(0, 200)}...`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to fetch backend config:', error);
    // Return a default config if API fails
    return {
      enable_signup: true,
      enable_web_search: false,
      features: {
        enable_ldap: false
      }
    };
  }
};

// Version updates API
export const getVersionUpdates = async (): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/version/updates`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Version updates API error:', response.status, errorData);
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to fetch version updates:', error);
    // Return empty updates if API fails
    return [];
  }
};

// Tool servers data API
export const getToolServersData = async (token: string): Promise<any> => {
  try {
    const response = await apiClient.get('/tools/servers', token);
    return response;
  } catch (error) {
    console.error('Failed to fetch tool servers data:', error);
    throw error;
  }
};

// Execute tool server API
export const executeToolServer = async (
  token: string,
  url: string,
  toolName: string,
  params: any,
  toolServerData?: any
): Promise<any> => {
  try {
    const response = await apiClient.post(
      '/tools/execute',
      {
        url,
        tool_name: toolName,
        params,
        tool_server_data: toolServerData,
      },
      token
    );
    return response;
  } catch (error) {
    console.error('Failed to execute tool server:', error);
    throw error;
  }
};

// Chat completion API
export const chatCompleted = async (
  token: string,
  chatId: string,
  data: any
): Promise<any> => {
  try {
    const response = await apiClient.post(
      `/chats/${chatId}/completed`,
      data,
      token
    );
    return response;
  } catch (error) {
    console.error('Failed to mark chat as completed:', error);
    throw error;
  }
};

// Generate queries API
export const generateQueries = async (
  token: string,
  data: any
): Promise<any> => {
  try {
    const response = await apiClient.post('/generate/queries', data, token);
    return response;
  } catch (error) {
    console.error('Failed to generate queries:', error);
    throw error;
  }
};

// Chat action API
export const chatAction = async (
  token: string,
  chatId: string,
  action: string,
  data: any
): Promise<any> => {
  try {
    const response = await apiClient.post(
      `/chats/${chatId}/actions/${action}`,
      data,
      token
    );
    return response;
  } catch (error) {
    console.error('Failed to execute chat action:', error);
    throw error;
  }
};

// Generate MoA completion API
export const generateMoACompletion = async (
  token: string,
  data: any
): Promise<any> => {
  try {
    const response = await apiClient.post('/generate/moa', data, token);
    return response;
  } catch (error) {
    console.error('Failed to generate MoA completion:', error);
    throw error;
  }
};

// Stop task API
export const stopTask = async (token: string, taskId: string): Promise<any> => {
  try {
    const response = await apiClient.post(`/tasks/${taskId}/stop`, {}, token);
    return response;
  } catch (error) {
    console.error('Failed to stop task:', error);
    throw error;
  }
};

// Get task IDs by chat ID API
export const getTaskIdsByChatId = async (
  token: string,
  chatId: string
): Promise<any> => {
  try {
    const response = await apiClient.get(`/chats/${chatId}/tasks`, token);
    return response;
  } catch (error) {
    console.error('Failed to get task IDs by chat ID:', error);
    throw error;
  }
};

// Auto-completion API
export const generateAutoCompletion = async (
  token: string = '',
  model: string,
  prompt: string,
  messages?: any[],
  type: string = 'search query'
): Promise<string> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/v1/tasks/auto/completions`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        model: model,
        prompt: prompt,
        ...(messages && { messages: messages }),
        type: type,
        stream: false
      })
    });

    if (!response.ok) {
      throw await response.json();
    }

    const result = await response.json();
    return result?.choices?.[0]?.message?.content || '';
  } catch (error) {
    console.error('Failed to generate auto completion:', error);
    return '';
  }
};

// Chat APIs
export const getChatList = async (
  token: string,
  page?: number
): Promise<any[]> => {
  try {
    const searchParams = new URLSearchParams();
    if (page !== null && page !== undefined) {
      searchParams.append('page', `${page}`);
    }
    
    const endpoint = `/chats/${searchParams.toString() ? '?' + searchParams.toString() : ''}`;
    const response = await apiClient.get(endpoint, token);
    return response;
  } catch (error) {
    console.error('Failed to fetch chat list:', error);
    throw error;
  }
};

export const getChatById = async (
  token: string,
  chatId: string
): Promise<any> => {
  try {
    const response = await apiClient.get(`/chats/${chatId}`, token);
    return response;
  } catch (error) {
    console.error('Failed to fetch chat by ID:', error);
    throw error;
  }
};

export const updateChatById = async (
  token: string,
  chatId: string,
  updates: any
): Promise<any> => {
  try {
    const response = await apiClient.post(`/chats/${chatId}`, { chat: updates }, token);
    return response;
  } catch (error) {
    console.error('Failed to update chat:', error);
    throw error;
  }
};

export const deleteChatById = async (
  token: string,
  chatId: string
): Promise<boolean> => {
  try {
    const response = await apiClient.delete(`/chats/${chatId}`, token);
    return response;
  } catch (error) {
    console.error('Failed to delete chat:', error);
    throw error;
  }
};

export const cloneChatById = async (
  token: string,
  chatId: string,
  title?: string
): Promise<any> => {
  try {
    const response = await apiClient.post(
      `/chats/${chatId}/clone`,
      title ? { title } : {},
      token
    );
    return response;
  } catch (error) {
    console.error('Failed to clone chat:', error);
    throw error;
  }
};

export const shareChatById = async (
  token: string,
  chatId: string
): Promise<any> => {
  try {
    const response = await apiClient.post(`/chats/${chatId}/share`, {}, token);
    return response;
  } catch (error) {
    console.error('Failed to share chat:', error);
    throw error;
  }
};

export const archiveChatById = async (
  token: string,
  chatId: string
): Promise<any> => {
  try {
    const response = await apiClient.post(`/chats/${chatId}/archive`, {}, token);
    return response;
  } catch (error) {
    console.error('Failed to archive chat:', error);
    throw error;
  }
};

export const toggleChatPinnedById = async (
  token: string,
  chatId: string
): Promise<any> => {
  try {
    const response = await apiClient.post(`/chats/${chatId}/pin`, {}, token);
    return response;
  } catch (error) {
    console.error('Failed to toggle chat pin status:', error);
    throw error;
  }
};

export const getPinnedChatList = async (
  token: string
): Promise<any[]> => {
  try {
    const response = await apiClient.get('/chats/pinned', token);
    return response;
  } catch (error) {
    console.error('Failed to fetch pinned chats:', error);
    throw error;
  }
};

export const searchChats = async (
  token: string,
  text: string,
  page: number = 1
): Promise<any[]> => {
  try {
    const searchParams = new URLSearchParams();
    searchParams.append('text', text);
    searchParams.append('page', `${page}`);
    
    const response = await apiClient.get(`/chats/search?${searchParams.toString()}`, token);
    return response;
  } catch (error) {
    console.error('Failed to search chats:', error);
    throw error;
  }
};

// Generate chat title
export const generateTitle = async (
  token: string,
  model: string,
  messages: any[]
): Promise<string | null> => {
  try {
    const titlePrompt = 'Create a brief, descriptive title for this conversation in 3-7 words. Respond with only the title, no quotes or explanations.';
    
    const response = await generateAutoCompletion(
      token,
      model,
      titlePrompt,
      messages,
      'title generation'
    );
    
    return response.trim() || null;
  } catch (error) {
    console.error('Failed to generate title:', error);
    return null;
  }
};

// Get all tags
export const getAllTags = async (
  token: string
): Promise<any[]> => {
  try {
    const response = await apiClient.get('/chats/all/tags', token);
    return response;
  } catch (error) {
    console.error('Failed to fetch all tags:', error);
    throw error;
  }
};

// Add tag to chat
export const addTagById = async (
  token: string,
  chatId: string,
  tagName: string
): Promise<any[]> => {
  try {
    const response = await apiClient.post(
      `/chats/${chatId}/tags`,
      { name: tagName },
      token
    );
    return response;
  } catch (error) {
    console.error('Failed to add tag to chat:', error);
    throw error;
  }
};

// Delete tag from chat
export const deleteTagById = async (
  token: string,
  chatId: string,
  tagName: string
): Promise<any[]> => {
  try {
    const response = await apiClient.delete(
      `/chats/${chatId}/tags`,
      token
    );
    // Note: The backend expects the tag name in the request body for DELETE
    // This might need adjustment based on the actual backend implementation
    return response;
  } catch (error) {
    console.error('Failed to delete tag from chat:', error);
    throw error;
  }
};

// ===========================================
// Audio APIs
// ===========================================

export const getAudioConfig = async (token: string) => {
  try {
    const response = await fetch(`${AUDIO_API_BASE_URL}/config`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get audio config:', error);
    throw error;
  }
};

type OpenAIConfigForm = {
  url: string;
  key: string;
  model: string;
  speaker: string;
};

export const updateAudioConfig = async (token: string, payload: OpenAIConfigForm) => {
  try {
    const response = await fetch(`${AUDIO_API_BASE_URL}/config/update`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update audio config:', error);
    throw error;
  }
};

export const transcribeAudio = async (token: string, file: File) => {
  try {
    const data = new FormData();
    data.append('file', file);

    const response = await fetch(`${AUDIO_API_BASE_URL}/transcriptions`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        authorization: `Bearer ${token}`
      },
      body: data
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to transcribe audio:', error);
    throw error;
  }
};

export const synthesizeOpenAISpeech = async (
  token: string = '',
  speaker: string = 'alloy',
  text: string = '',
  model?: string
) => {
  try {
    const response = await fetch(`${AUDIO_API_BASE_URL}/speech`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        input: text,
        voice: speaker,
        ...(model && { model })
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  } catch (error) {
    console.error('Failed to synthesize speech:', error);
    throw error;
  }
};

interface AvailableModelsResponse {
  models: { name: string; id: string }[] | { id: string }[];
}

export const getAudioModels = async (token: string = ''): Promise<AvailableModelsResponse> => {
  try {
    const response = await fetch(`${AUDIO_API_BASE_URL}/models`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get audio models:', error);
    throw error;
  }
};

export const getVoices = async (token: string = '') => {
  try {
    const response = await fetch(`${AUDIO_API_BASE_URL}/voices`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get voices:', error);
    throw error;
  }
};
