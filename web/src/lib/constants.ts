// ===========================================
// App Configuration - 从环境变量读取
// ===========================================
export const APP_NAME = process.env.NEXT_PUBLIC_APP_NAME || 'K2';

// ===========================================
// API Base URLs - 完全基于环境变量配置
// ===========================================

// 主 API 配置
export const WEBUI_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api/v1';

// 从主 API URL 提取基础 URL（兼容原有逻辑）
const getWebUIBaseUrl = () => {
  const apiUrl = WEBUI_API_BASE_URL;
  // 移除 /api/v1 后缀，获取基础 URL
  return apiUrl.replace(/\/api\/v1$/, '');
};

export const WEBUI_BASE_URL = getWebUIBaseUrl();
export const WEBUI_HOSTNAME = (() => {
  try {
    const url = new URL(WEBUI_BASE_URL);
    return url.host;
  } catch {
    return 'localhost:8080';
  }
})();

// AI 模型服务 URLs
export const OLLAMA_API_BASE_URL = process.env.NEXT_PUBLIC_OLLAMA_API_URL || `${WEBUI_BASE_URL}/ollama`;
export const OPENAI_API_BASE_URL = process.env.NEXT_PUBLIC_OPENAI_API_URL || `${WEBUI_BASE_URL}/openai`;

// 媒体和内容服务 URLs
export const AUDIO_API_BASE_URL = process.env.NEXT_PUBLIC_AUDIO_API_URL || `${WEBUI_BASE_URL}/api/v1/audio`;
export const IMAGES_API_BASE_URL = process.env.NEXT_PUBLIC_IMAGES_API_URL || `${WEBUI_BASE_URL}/api/v1/images`;
export const RETRIEVAL_API_BASE_URL = process.env.NEXT_PUBLIC_RETRIEVAL_API_URL || `${WEBUI_BASE_URL}/api/v1/retrieval`;

// ===========================================
// WebSocket 配置
// ===========================================
// WebSocket connection is handled via Socket.IO using WEBUI_API_BASE_URL

// ===========================================
// 功能开关配置
// ===========================================
export const ENABLE_COMMUNITY_SHARING = process.env.NEXT_PUBLIC_ENABLE_COMMUNITY_SHARING === 'true';
export const ENABLE_DIRECT_CONNECTIONS = process.env.NEXT_PUBLIC_ENABLE_DIRECT_CONNECTIONS === 'true';
export const ENABLE_DEBUG_LOGS = process.env.NEXT_PUBLIC_ENABLE_DEBUG_LOGS === 'true';

// ===========================================
// 版本信息配置
// ===========================================
export const WEBUI_VERSION = process.env.NEXT_PUBLIC_APP_VERSION || '0.6.9';
export const WEBUI_BUILD_HASH = process.env.NEXT_PUBLIC_BUILD_HASH || 'dev';
export const REQUIRED_OLLAMA_VERSION = '0.1.16';

// ===========================================
// API 请求配置
// ===========================================
export const API_TIMEOUT = parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '30000', 10);

// Supported file types
export const SUPPORTED_FILE_TYPE = [
  'application/epub+zip',
  'application/pdf',
  'text/plain',
  'text/csv',
  'text/xml',
  'text/html',
  'text/x-python',
  'text/css',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/octet-stream',
  'application/x-javascript',
  'text/markdown',
  'audio/mpeg',
  'audio/wav',
  'audio/ogg',
  'audio/x-m4a'
];

export const SUPPORTED_FILE_EXTENSIONS = [
  'md',
  'rst',
  'go',
  'py',
  'java',
  'sh',
  'bat',
  'ps1',
  'cmd',
  'js',
  'ts',
  'css',
  'cpp',
  'hpp',
  'h',
  'c',
  'cs',
  'htm',
  'html',
  'sql',
  'log',
  'ini',
  'pl',
  'pm',
  'r',
  'dart',
  'dockerfile',
  'env',
  'php',
  'hs',
  'hsc',
  'lua',
  'nginxconf',
  'conf',
  'm',
  'mm',
  'plsql',
  'perl',
  'rb',
  'rs',
  'db2',
  'scala',
  'bash',
  'swift',
  'vue',
  'svelte',
  'doc',
  'docx',
  'pdf',
  'csv',
  'txt',
  'xls',
  'xlsx',
  'pptx',
  'ppt',
  'msg'
];

// 与原项目保持一致的常量
export const PASTED_TEXT_CHARACTER_LIMIT = 1000;

// UI Constants
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

// Chat Constants
export const MAX_MESSAGE_LENGTH = 4000;
export const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB

// Theme Constants
export const THEMES = ['light', 'dark', 'system'] as const;
export type Theme = typeof THEMES[number];

// Default Settings
export const DEFAULT_SETTINGS = {
  ui: {
    theme: 'system',
    language: 'en',
    fontSize: 'medium',
    codeTheme: 'github-dark',
    showTimestamp: true,
    showUsername: true,
  },
  notifications: {
    enabled: true,
    sounds: true,
    desktop: false,
  },
  audio: {
    // TTS Settings
    ttsEnabled: false,
    ttsAutoPlay: false,
    ttsVoice: '',
    ttsSpeed: 1,
    ttsPitch: 1,
    ttsVolume: 1,
    // STT Settings
    sttEnabled: false,
    sttAutoSend: false,
    sttAutoTranscribe: true,
    sttLanguage: 'en-US',
    sttEngine: 'browser',
    // Audio Notifications
    audioNotifications: true,
    audioNotificationVolume: 0.5,
    customNotificationSound: null,
  },
  // Speech settings for compatibility with original project
  speechAutoSend: false,
  speechAutoTranscribe: true,
};

// Storage Keys
export const STORAGE_KEYS = {
  USER: 'user',
  SETTINGS: 'settings',
  THEME: 'theme',
  CHATS: 'chats',
  MODELS: 'models',
  TOKEN: 'token',
} as const;
