@import 'tailwindcss';

/* 原项目的字体定义 */
@font-face {
  font-family: 'Inter';
  src: url('/assets/fonts/Inter-Variable.ttf');
  font-display: swap;
}

@font-face {
  font-family: 'Archivo';
  src: url('/assets/fonts/Archivo-Variable.ttf');
  font-display: swap;
}

@font-face {
  font-family: 'Mona Sans';
  src: url('/assets/fonts/Mona-Sans.woff2');
  font-display: swap;
}

@font-face {
  font-family: 'InstrumentSerif';
  src: url('/assets/fonts/InstrumentSerif-Regular.ttf');
  font-display: swap;
}

@font-face {
  font-family: 'Vazirmatn';
  src: url('/assets/fonts/Vazirmatn-Variable.ttf');
  font-display: swap;
}

/* 原项目的颜色变量 */
:root {
  --color-gray-50: #f9f9f9;
  --color-gray-100: #ececec;
  --color-gray-200: #e3e3e3;
  --color-gray-300: #cdcdcd;
  --color-gray-400: #b4b4b4;
  --color-gray-500: #9b9b9b;
  --color-gray-600: #676767;
  --color-gray-700: #4e4e4e;
  --color-gray-800: #333;
  --color-gray-850: #262626;
  --color-gray-900: #171717;
  --color-gray-950: #0d0d0d;
}

/* 基础样式 - 与原项目保持一致 */
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@layer base {
  html,
  pre {
    font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'Vazirmatn', ui-sans-serif, system-ui,
      'Segoe UI', Roboto, Ubuntu, Cantarell, 'Noto Sans', sans-serif, 'Helvetica Neue', Arial,
      'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }

  pre {
    white-space: pre-wrap;
  }

  button {
    @apply cursor-pointer;
  }

  input::placeholder,
  textarea::placeholder {
    color: var(--color-gray-400);
  }
}

html {
  word-break: break-word;
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

code {
  overflow-x: auto;
  width: auto;
}

.font-secondary {
  font-family: 'InstrumentSerif', sans-serif;
}

.font-primary {
  font-family: 'Archivo', 'Vazirmatn', sans-serif;
}

math {
  margin-top: 1rem;
}

.hljs {
  @apply rounded-lg;
}

input::placeholder {
  direction: auto;
}

textarea::placeholder {
  direction: auto;
}

/* 原项目的 prose 样式类 - 暂时注释掉直到typography插件正确配置 */
/*
.input-prose {
  @apply prose dark:prose-invert prose-headings:font-semibold prose-hr:my-4 prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-0 prose-img:my-1 prose-headings:my-1 prose-pre:my-0 prose-table:my-0 prose-blockquote:my-0 prose-ul:-my-0 prose-ol:-my-0 prose-li:-my-0 whitespace-pre-line;
}

.input-prose-sm {
  @apply prose dark:prose-invert prose-headings:font-semibold prose-hr:my-4 prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-0 prose-img:my-1 prose-headings:my-1 prose-pre:my-0 prose-table:my-0 prose-blockquote:my-0 prose-ul:-my-0 prose-ol:-my-0 prose-li:-my-0 whitespace-pre-line text-sm;
}

.markdown-prose {
  @apply prose dark:prose-invert prose-blockquote:border-s-gray-100 prose-blockquote:dark:border-gray-800 prose-blockquote:border-s-2 prose-blockquote:not-italic prose-blockquote:font-normal  prose-headings:font-semibold prose-hr:my-4 prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-0 prose-img:my-1 prose-headings:my-1 prose-pre:my-0 prose-table:my-0 prose-blockquote:my-0 prose-ul:-my-0 prose-ol:-my-0 prose-li:-my-0 whitespace-pre-line;
}

.markdown-prose-xs {
  @apply text-xs prose dark:prose-invert prose-blockquote:border-s-gray-100 prose-blockquote:dark:border-gray-800 prose-blockquote:border-s-2 prose-blockquote:not-italic prose-blockquote:font-normal  prose-headings:font-semibold prose-hr:my-0  prose-hr:border-gray-100 prose-hr:dark:border-gray-800 prose-p:my-0 prose-img:my-1 prose-headings:my-1 prose-pre:my-0 prose-table:my-0 prose-blockquote:my-0 prose-ul:-my-0 prose-ol:-my-0 prose-li:-my-0 whitespace-pre-line;
}
*/

.markdown a {
  @apply underline;
}

/* 自定义动画 */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: scale(0.985);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.1s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* 滚动条样式 */
.scrollbar-hidden {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

/* 阴影样式 */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

.shadow-mini {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Electron 拖拽区域 */
.drag-region {
  -webkit-app-region: drag;
}

.drag-region a,
.drag-region button {
  -webkit-app-region: no-drag;
}

.no-drag-region {
  -webkit-app-region: no-drag;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Code highlighting */
pre {
  background-color: hsl(var(--muted));
  border-radius: var(--radius);
  padding: 1rem;
  overflow-x: auto;
}

code {
  background-color: hsl(var(--muted));
  padding: 0.2rem 0.4rem;
  border-radius: calc(var(--radius) - 2px);
  font-size: 0.875em;
}

pre code {
  background-color: transparent;
  padding: 0;
}

/* Markdown styles */
.prose {
  max-width: none;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: hsl(var(--foreground));
}

.prose p {
  color: hsl(var(--foreground));
}

.prose a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.prose a:hover {
  color: hsl(var(--primary) / 0.8);
}

.prose blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.prose ul,
.prose ol {
  color: hsl(var(--foreground));
}

.prose li {
  margin: 0.5rem 0;
}

.prose table {
  border-collapse: collapse;
  width: 100%;
}

.prose th,
.prose td {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
  text-align: left;
}

.prose th {
  background-color: hsl(var(--muted));
  font-weight: 600;
}
