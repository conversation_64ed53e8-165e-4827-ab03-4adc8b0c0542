'use client';

import { useCallback, useRef, useState, useEffect } from 'react';
import { motion } from 'framer-motion';

import { AgentInputBox } from '@/components/agent/AgentInputBox';
import { AgentMessageList } from '@/components/agent/AgentMessageList';
import { AgentWelcome } from '@/components/agent/AgentWelcome';
import { ResearchBlock } from '@/components/agent/ResearchBlock';
import type { Message, Option, Resource } from '@/lib/agent/types';
import { useWebSocket } from '@/hooks/useWebSocket';
import { cn } from '@/lib/utils';

// Conversation starter questions
const questions = [
  "Review the resumes and rank the candidates by expertise.",
  "List properties for purchase in a safe neighborhood in New York.",
  "Analyze stock price correlations of NVDA, MRVL, and TSM over the past 3 years.",
];

function ConversationStarter({
  className,
  onSend,
  onFillInput,
}: {
  className?: string;
  onSend?: (message: string) => void;
  onFillInput?: (message: string) => void;
}) {
  return (
    <div className={cn("w-full", className)}>
      <div className="mb-6 flex justify-center absolute left-0 right-0 top-[calc(50%-200px)]">
        <AgentWelcome className="w-full max-w-[75%]" />
      </div>
      <ul className="flex flex-col">
        {questions.map((question, index) => (
          <motion.li
            key={question}
            className="w-full"
            style={{ transition: "all 0.2s ease-out" }}
            initial={{ opacity: 0, y: 24 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{
              duration: 0.2,
              delay: index * 0.1 + 0.5,
              ease: "easeOut",
            }}
          >
            <div
              className="cursor-pointer p-[12px] text-sm opacity-75 transition-all duration-300 hover:opacity-100 h-full flex items-center text-[#343A3E]"
              onClick={() => {
                onFillInput?.(question);
              }}
            >
              {question}
            </div>
          </motion.li>
        ))}
      </ul>
    </div>
  );
}

// Mock uploaded files management
interface UploadedFile {
  id: string;
  name: string;
  size: string;
  type: string;
  file: File;
}

interface KnowledgeFile {
  id: string;
  title: string;
  size: string;
  type: 'knowledge';
  uri: string;
}

function UploadedFiles({
  files,
  knowledgeFiles,
  onRemoveFile,
  onRemoveKnowledgeFile,
}: {
  files: UploadedFile[];
  knowledgeFiles: KnowledgeFile[];
  onRemoveFile: (id: string) => void;
  onRemoveKnowledgeFile: (id: string) => void;
}) {
  const allItems = [...files, ...knowledgeFiles];
  
  if (allItems.length === 0) return null;

  return (
    <div className="flex flex-col rounded-b-xl border-x border-b bg-[#F7F7FC] border-[#DCDFE6]">
      <div className="flex items-end gap-2 overflow-x-auto p-3">
        {files.map((file) => {
          const isImage = file.type.startsWith('image/');
          if (isImage) {
            return (
              <div key={file.id} className="relative group cursor-pointer" style={{ width: '60px', height: '60px', flexShrink: 0 }}>
                <img src={URL.createObjectURL(file.file)} alt={file.name} className="w-full h-full object-cover rounded-lg" />
                <button 
                  onClick={() => onRemoveFile(file.id)} 
                  className="absolute top-[-4px] right-[-4px] w-5 h-5 bg-black rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <span className="text-white text-xs">×</span>
                </button>
              </div>
            );
          }
          return (
            <div 
              key={file.id} 
              className="relative flex items-center gap-2 p-2 bg-white rounded-lg border group cursor-pointer" 
              style={{ borderColor: '#E5E5EF', width: '180px', height: '55px', flexShrink: 0 }}
            >
              <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                📄
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-normal truncate" style={{ color: '#343A3E' }}>
                  {file.name}
                </div>
                <div className="text-xs" style={{ color: '#9099B1' }}>
                  {file.size}
                </div>
              </div>
              <button 
                onClick={() => onRemoveFile(file.id)} 
                className="absolute top-[-4px] right-[-4px] w-5 h-5 bg-black rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <span className="text-white text-xs">×</span>
              </button>
            </div>
          );
        })}
        {knowledgeFiles.map((kf) => (
          <div 
            key={kf.id} 
            className="relative flex items-center gap-2 p-2 bg-white rounded-lg border group cursor-pointer" 
            style={{ borderColor: '#E5E5EF', width: '180px', height: '55px', flexShrink: 0 }}
          >
            <div className="w-8 h-8 bg-blue-200 rounded flex items-center justify-center">
              📚
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-normal truncate" style={{ color: '#343A3E' }}>
                {kf.title}
              </div>
              <div className="text-xs" style={{ color: '#9099B1' }}>
                {kf.size}
              </div>
            </div>
            <button 
              onClick={() => onRemoveKnowledgeFile(kf.id)} 
              className="absolute top-[-4px] right-[-4px] w-5 h-5 bg-black rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <span className="text-white text-xs">×</span>
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}

export default function AgentPage() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [responding, setResponding] = useState(false);
  const [feedback, setFeedback] = useState<{ option: Option } | null>(null);
  const [openResearchId, setOpenResearchId] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [knowledgeFiles, setKnowledgeFiles] = useState<KnowledgeFile[]>([]);
  
  const inputBoxRef = useRef<any>(null);
  const { connect, disconnect, emit, on, off, isConnected } = useWebSocket();

  // Initialize WebSocket connection
  useEffect(() => {
    connect().catch(console.error);
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  const handleFillInput = useCallback((content: string) => {
    if (inputBoxRef.current) {
      inputBoxRef.current.fillContent(content);
    }
  }, []);

  const handleFileUpload = useCallback((files: FileList) => {
    const newFiles = Array.from(files).map(file => {
      const sizeInKB = Math.round(file.size / 1024);
      const sizeText = sizeInKB < 1024 ? `${sizeInKB}k` : `${Math.round(sizeInKB / 1024)}M`;
      return {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        size: sizeText,
        type: file.type,
        file
      };
    });
    setUploadedFiles(prev => [...prev, ...newFiles]);
  }, []);

  const handleKnowledgeFileSelect = useCallback((knowledgeFile: { id: string; title: string; size: string }) => {
    const newKnowledgeFile: KnowledgeFile = {
      id: `knowledge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: knowledgeFile.title,
      size: knowledgeFile.size,
      type: 'knowledge',
      uri: knowledgeFile.id
    };
    
    setKnowledgeFiles(prev => {
      const exists = prev.some(kf => kf.uri === knowledgeFile.id);
      if (!exists) {
        return [...prev, newKnowledgeFile];
      }
      return prev;
    });
  }, []);

  const handleSend = useCallback(async (
    message: string,
    options?: {
      interruptFeedback?: string;
      resources?: Array<Resource>;
      tools?: Array<string>;
    },
  ) => {
    if (!message.trim() || responding) return;

    // Create user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      threadId: 'current-thread',
      role: 'user',
      content: message,
      contentChunks: [message],
      isStreaming: false,
    };

    setMessages(prev => [...prev, userMessage]);
    setResponding(true);
    setFeedback(null);
    
    // Clear uploaded files
    setUploadedFiles([]);
    setKnowledgeFiles([]);

    try {
      // Mock AI response - in real implementation, this would use WebSocket
      setTimeout(() => {
        const aiMessage: Message = {
          id: `ai-${Date.now()}`,
          threadId: 'current-thread',
          role: 'assistant',
          agent: 'coordinator',
          content: `I understand you want me to help with: "${message}". Let me think about this and create a plan to assist you.`,
          contentChunks: [],
          isStreaming: false,
        };
        
        setMessages(prev => [...prev, aiMessage]);
        setResponding(false);
      }, 2000);
    } catch (error) {
      console.error('Failed to send message:', error);
      setResponding(false);
    }
  }, [responding]);

  const handleCancel = useCallback(() => {
    setResponding(false);
  }, []);

  const handleFeedback = useCallback((feedback: { option: Option }) => {
    setFeedback(feedback);
  }, []);

  const handleRemoveFeedback = useCallback(() => {
    setFeedback(null);
  }, []);

  const handleRemoveFile = useCallback((fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  }, []);

  const handleRemoveKnowledgeFile = useCallback((fileId: string) => {
    setKnowledgeFiles(prev => prev.filter(kf => kf.id !== fileId));
  }, []);

  const doubleColumnMode = openResearchId !== null;
  const messageCount = messages.length;
  const hasFiles = uploadedFiles.length > 0 || knowledgeFiles.length > 0;

  return (
    <div className="h-screen w-full overscroll-none bg-gray-50 overflow-x-hidden">
      <div className="h-full w-full px-4 pt-4 pb-4 overflow-x-hidden">
        <div className="h-full w-full max-w-4xl mx-auto flex">
          {/* Main messages area */}
          <div className="flex-1 flex flex-col min-w-0 relative">
            <AgentMessageList
              className="flex-grow"
              messages={messages}
              responding={responding}
              onFeedback={handleFeedback}
              onSendMessage={handleSend}
            />
            
            {/* Input area */}
            <div className={cn(
              !responding && messageCount === 0 && "absolute top-[calc(50%-150px)]", 
              "flex flex-col shrink-0 pb-4 w-full"
            )}>
              <AgentInputBox
                ref={inputBoxRef}
                className="w-full"
                responding={responding}
                feedback={feedback}
                onSend={handleSend}
                onCancel={handleCancel}
                onRemoveFeedback={handleRemoveFeedback}
                onFileUpload={handleFileUpload}
                onKnowledgeFileSelect={handleKnowledgeFileSelect}
                hasFilesBelow={hasFiles}
              />
              <UploadedFiles
                files={uploadedFiles}
                knowledgeFiles={knowledgeFiles}
                onRemoveFile={handleRemoveFile}
                onRemoveKnowledgeFile={handleRemoveKnowledgeFile}
              />
              {!responding && messageCount === 0 && (
                <ConversationStarter
                  className="mt-[24px]"
                  onSend={handleSend}
                  onFillInput={handleFillInput}
                />
              )}
            </div>
          </div>
          
          {/* Research panel */}
          {doubleColumnMode && (
            <div className="w-96 pl-8 pb-4 min-w-0">
              <ResearchBlock
                className="h-full w-full"
                researchId={openResearchId}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}