"use client";

import React, { useState, useEffect } from "react";
import { useAppStore, useChatStore } from "@/lib/stores";
import { Chat } from "@/components/chat";
// Agent functionality moved to /agent page
import { AppLayout } from "@/components/layout/AppLayout";
import { ConversationEntry } from "@/lib/types";
import { nanoid } from "nanoid";

function HomePageContent() {
  const { 
    selectedModels, 
    conversations, 
    addConversation, 
    updateConversation, 
    getConversation 
  } = useAppStore();
  const { setCurrentChat } = useChatStore();
  
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [selectedMode, setSelectedMode] = useState<'chat' | 'agent'>('chat');
  const currentConversation = currentConversationId ? getConversation(currentConversationId) : null;

  // Clear chat state when navigating to home page
  useEffect(() => {
    // Clear the current chat from useChatStore when we're on the home page
    // This ensures that when users navigate back from /c/[chatId] to home,
    // they see a fresh chat interface
    setCurrentChat(null);
  }, [setCurrentChat]);

  // Auto-create a new chat conversation if none exists
  useEffect(() => {
    if (conversations.length === 0) {
      handleNewConversation(selectedMode);
    } else if (!currentConversationId) {
      // Select the most recent conversation of the selected mode, or create one
      const conversationOfType = conversations.find(conv => conv.type === selectedMode);
      if (conversationOfType) {
        setCurrentConversationId(conversationOfType.id);
      } else {
        handleNewConversation(selectedMode);
      }
    }
  }, [conversations, currentConversationId, selectedMode]);

  // Update current conversation when mode changes
  useEffect(() => {
    if (currentConversation && currentConversation.type !== selectedMode) {
      // Look for existing conversation of the selected mode
      const conversationOfType = conversations.find(conv => conv.type === selectedMode);
      if (conversationOfType) {
        setCurrentConversationId(conversationOfType.id);
      } else {
        // Create new conversation of selected mode
        handleNewConversation(selectedMode);
      }
    }
  }, [selectedMode, currentConversation, conversations]);

  const handleNewConversation = (type: 'chat' | 'agent' = 'chat') => {
    const newConversation: ConversationEntry = {
      id: nanoid(),
      type,
      timestamp: Date.now(),
      title: type === 'chat' ? 'New Chat' : 'New Agent Research',
      lastMessage: '',
      ...(type === 'chat' 
        ? { 
            chatData: { 
              messages: [], 
              selectedModels: selectedModels 
            }
          }
        : { 
            agentData: { 
              messages: [], 
              threadId: nanoid(),
              researchIds: [] 
            }
          }
      )
    };

    addConversation(newConversation);
    setCurrentConversationId(newConversation.id);
  };

  const handleConversationSelect = (id: string) => {
    setCurrentConversationId(id);
  };

  const handleConversationUpdate = (updates: Partial<ConversationEntry>) => {
    if (currentConversationId) {
      updateConversation(currentConversationId, updates);
    }
  };

  if (!currentConversation) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 mb-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Mode Selector */}
      <div className="flex items-center justify-center gap-2 p-4 border-b bg-white dark:bg-gray-900">
        <button
          onClick={() => setSelectedMode('chat')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            selectedMode === 'chat' 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
          }`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          Chat Mode
        </button>
        <button
          onClick={() => window.location.href = '/agent'}
          className="flex items-center gap-2 px-4 py-2 rounded-lg transition-colors bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          Agent Mode
        </button>
      </div>
      
      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        <Chat 
          chatId={currentConversation.id}
          selectedModels={currentConversation.chatData?.selectedModels || selectedModels}
          initialMessages={currentConversation.chatData?.messages || []}
          onUpdate={handleConversationUpdate}
        />
      </div>
    </div>
  );
}

export default function Home() {
  const { 
    getConversation,
    conversations
  } = useAppStore();
  
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  // Auto-select first conversation if available
  useEffect(() => {
    if (conversations.length > 0 && !currentConversationId) {
      setCurrentConversationId(conversations[0].id);
    }
  }, [conversations, currentConversationId]);

  const handleNewConversation = (type: 'chat' | 'agent' = 'chat') => {
    // This will be handled by HomePageContent
    window.location.reload(); // Temporary solution to trigger new conversation
  };

  const handleConversationSelect = (id: string) => {
    setCurrentConversationId(id);
  };

  return (
    <AppLayout>
      <div className="h-full">
        <HomePageContent />
      </div>
    </AppLayout>
  );
}
