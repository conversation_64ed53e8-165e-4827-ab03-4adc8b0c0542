"use client";

import { AppLayout } from "@/components/layout/AppLayout";
import { Chat } from "@/components/chat";
import { useAppStore } from "@/lib/stores";
import { use } from "react";

interface ChatPageProps {
  params: Promise<{
    chatId: string;
  }>;
  searchParams: Record<string, string | string[] | undefined>;
}

function ChatPageContent({ chatId }: { chatId: string }) {
  const { selectedModels } = useAppStore();

  // Validate chatId format (basic check)
  if (!chatId || chatId.trim() === '') {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Invalid Chat ID
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            The chat ID provided is not valid.
          </p>
        </div>
      </div>
    );
  }

  return (
    <Chat
      chatId={chatId}
      selectedModels={selectedModels}
    />
  );
}

export default function ChatPage({ params }: ChatPageProps) {
  const { chatId } = use(params);

  return (
    <AppLayout>
      <ChatPageContent chatId={chatId} />
    </AppLayout>
  );
}