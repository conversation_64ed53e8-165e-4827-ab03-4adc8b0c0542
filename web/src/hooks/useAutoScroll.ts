import { useState, useRef, useCallback, useEffect } from 'react';

interface UseAutoScrollOptions {
  autoScroll?: boolean;
  bottomPadding?: boolean;
  messages?: any[];
}

interface UseAutoScrollReturn {
  isAutoScrollEnabled: boolean;
  userScrolledUp: boolean;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  bottomRef: React.RefObject<HTMLDivElement>;
  scrollToBottom: (smooth?: boolean) => void;
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
  setIsAutoScrollEnabled: (enabled: boolean) => void;
  setUserScrolledUp: (scrolledUp: boolean) => void;
}

export const useAutoScroll = ({
  autoScroll = true,
  bottomPadding = false,
  messages = []
}: UseAutoScrollOptions = {}): UseAutoScrollReturn => {
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [userScrolledUp, setUserScrolledUp] = useState(false);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const lastScrollTop = useRef<number>(0);
  const programmaticScrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTime = useRef<number>(0);

  // Auto scroll to bottom when new messages arrive (only if auto-scroll is enabled)
  useEffect(() => {
    if (autoScroll && bottomPadding && isAutoScrollEnabled && !userScrolledUp && bottomRef.current) {
      // Clear any existing timeout
      if (programmaticScrollTimeoutRef.current) {
        clearTimeout(programmaticScrollTimeoutRef.current);
      }

      // Mark the time of programmatic scroll
      lastScrollTime.current = Date.now();

      bottomRef.current.scrollIntoView({ behavior: 'smooth' });

      // Set a timeout to allow user interruption after a short delay
      programmaticScrollTimeoutRef.current = setTimeout(() => {
        lastScrollTime.current = 0; // Reset after delay
      }, 100); // Much shorter delay
    }
  }, [messages, autoScroll, bottomPadding, isAutoScrollEnabled, userScrolledUp]);

  const scrollToBottom = useCallback((smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      // Clear any existing timeout
      if (programmaticScrollTimeoutRef.current) {
        clearTimeout(programmaticScrollTimeoutRef.current);
      }

      // Mark the time of programmatic scroll
      lastScrollTime.current = Date.now();

      if (smooth) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }

      // Re-enable auto-scroll when manually scrolling to bottom
      setUserScrolledUp(false);
      setIsAutoScrollEnabled(true);

      // Set a timeout to allow user interruption after a short delay
      programmaticScrollTimeoutRef.current = setTimeout(() => {
        lastScrollTime.current = 0; // Reset after delay
      }, 100);
    }
  }, []);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = element;
    const currentTime = Date.now();

    // Check if this might be a programmatic scroll (within 100ms of last programmatic scroll)
    const isPossiblyProgrammatic = lastScrollTime.current > 0 && (currentTime - lastScrollTime.current) < 100;

    // Always update lastScrollTop for next comparison
    const previousScrollTop = lastScrollTop.current;
    lastScrollTop.current = scrollTop;

    // If this might be programmatic, don't process user scroll logic yet
    if (isPossiblyProgrammatic) {
      return;
    }

    // Check if user scrolled up manually
    const isScrollingUp = scrollTop < previousScrollTop;
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold

    if (isScrollingUp && !isAtBottom) {
      // User scrolled up manually, disable auto-scroll immediately
      setUserScrolledUp(true);
      setIsAutoScrollEnabled(false);
      // Clear any pending programmatic scroll timeout
      if (programmaticScrollTimeoutRef.current) {
        clearTimeout(programmaticScrollTimeoutRef.current);
        lastScrollTime.current = 0;
      }
    } else if (isAtBottom) {
      // User scrolled back to bottom, re-enable auto-scroll
      setUserScrolledUp(false);
      setIsAutoScrollEnabled(true);
    }
  }, []);

  return {
    isAutoScrollEnabled,
    userScrolledUp,
    messagesContainerRef,
    bottomRef,
    scrollToBottom,
    handleScroll,
    setIsAutoScrollEnabled,
    setUserScrolledUp
  };
};
