import { useState, useRef, useCallback, useEffect } from 'react';

interface UseAutoScrollOptions {
  autoScroll?: boolean;
  bottomPadding?: boolean;
  messages?: any[];
}

interface UseAutoScrollReturn {
  isAutoScrollEnabled: boolean;
  userScrolledUp: boolean;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  bottomRef: React.RefObject<HTMLDivElement>;
  scrollToBottom: (smooth?: boolean) => void;
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
  setIsAutoScrollEnabled: (enabled: boolean) => void;
  setUserScrolledUp: (scrolledUp: boolean) => void;
}

export const useAutoScroll = ({
  autoScroll = true,
  bottomPadding = false,
  messages = []
}: UseAutoScrollOptions = {}): UseAutoScrollReturn => {
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [userScrolledUp, setUserScrolledUp] = useState(false);
  
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const lastScrollTop = useRef<number>(0);
  const isScrollingProgrammatically = useRef<boolean>(false);

  // Auto scroll to bottom when new messages arrive (only if auto-scroll is enabled)
  useEffect(() => {
    if (autoScroll && bottomPadding && isAutoScrollEnabled && !userScrolledUp && bottomRef.current) {
      isScrollingProgrammatically.current = true;
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
      // Reset the flag after a short delay
      setTimeout(() => {
        isScrollingProgrammatically.current = false;
      }, 500);
    }
  }, [messages, autoScroll, bottomPadding, isAutoScrollEnabled, userScrolledUp]);

  const scrollToBottom = useCallback((smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      isScrollingProgrammatically.current = true;
      if (smooth) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
      // Re-enable auto-scroll when manually scrolling to bottom
      setUserScrolledUp(false);
      setIsAutoScrollEnabled(true);
      // Reset the flag after a short delay
      setTimeout(() => {
        isScrollingProgrammatically.current = false;
      }, 500);
    }
  }, []);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = element;
    
    // Skip if this is a programmatic scroll
    if (isScrollingProgrammatically.current) {
      lastScrollTop.current = scrollTop;
      return;
    }
    
    // Check if user scrolled up manually
    const isScrollingUp = scrollTop < lastScrollTop.current;
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold
    
    if (isScrollingUp && !isAtBottom) {
      // User scrolled up manually, disable auto-scroll
      setUserScrolledUp(true);
      setIsAutoScrollEnabled(false);
    } else if (isAtBottom) {
      // User scrolled back to bottom, re-enable auto-scroll
      setUserScrolledUp(false);
      setIsAutoScrollEnabled(true);
    }
    
    lastScrollTop.current = scrollTop;
  }, []);

  return {
    isAutoScrollEnabled,
    userScrolledUp,
    messagesContainerRef,
    bottomRef,
    scrollToBottom,
    handleScroll,
    setIsAutoScrollEnabled,
    setUserScrolledUp
  };
};
