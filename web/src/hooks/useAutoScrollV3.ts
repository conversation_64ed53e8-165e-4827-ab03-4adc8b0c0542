import { useState, useRef, useCallback, useEffect } from 'react';

interface UseAutoScrollOptions {
  autoScroll?: boolean;
  messages?: any[];
}

interface UseAutoScrollReturn {
  userScrolledUp: boolean;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  scrollToBottom: (smooth?: boolean) => void;
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

export const useAutoScrollV3 = ({
  autoScroll = true,
  messages = []
}: UseAutoScrollOptions = {}): UseAutoScrollReturn => {
  const [userScrolledUp, setUserScrolledUp] = useState(false);
  
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const lastScrollTop = useRef<number>(0);
  const isScrollingProgrammatically = useRef<boolean>(false);

  // Check if user is at bottom
  const isAtBottom = useCallback(() => {
    if (!messagesContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    return scrollHeight - scrollTop - clientHeight < 50;
  }, []);

  // Scroll to bottom function
  const scrollToBottom = useCallback((smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      isScrollingProgrammatically.current = true;
      
      if (smooth) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
      
      setUserScrolledUp(false);
      
      // Reset flag after a short delay
      setTimeout(() => {
        isScrollingProgrammatically.current = false;
      }, 100);
    }
  }, []);

  // Auto scroll when messages change
  useEffect(() => {
    if (autoScroll && !userScrolledUp && messagesContainerRef.current) {
      // Use a small delay to ensure DOM is updated
      const timer = setTimeout(() => {
        scrollToBottom(false);
      }, 10);
      
      return () => clearTimeout(timer);
    }
  }, [messages, autoScroll, userScrolledUp, scrollToBottom]);

  // Handle scroll events
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    if (isScrollingProgrammatically.current) {
      return;
    }

    const element = e.currentTarget;
    const { scrollTop } = element;
    
    // Check if user scrolled up
    const isScrollingUp = scrollTop < lastScrollTop.current;
    const atBottom = isAtBottom();
    
    if (isScrollingUp && !atBottom) {
      setUserScrolledUp(true);
    } else if (atBottom) {
      setUserScrolledUp(false);
    }
    
    lastScrollTop.current = scrollTop;
  }, [isAtBottom]);

  // Initial scroll to bottom
  useEffect(() => {
    const timer = setTimeout(() => {
      scrollToBottom(false);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []); // Only run once on mount

  return {
    userScrolledUp,
    messagesContainerRef,
    scrollToBottom,
    handleScroll
  };
};
