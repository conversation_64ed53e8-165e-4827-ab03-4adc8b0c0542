import { useState, useRef, useCallback, useEffect } from 'react';

interface UseAutoScrollOptions {
  autoScroll?: boolean;
  messages?: any[];
}

interface UseAutoScrollReturn {
  userScrolledUp: boolean;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  scrollToBottom: (smooth?: boolean) => void;
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

export const useAutoScrollV2 = ({
  autoScroll = true,
  messages = []
}: UseAutoScrollOptions = {}): UseAutoScrollReturn => {
  const [userScrolledUp, setUserScrolledUp] = useState(false);
  
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const isUserScrolling = useRef<boolean>(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check if user is at bottom
  const isAtBottom = useCallback(() => {
    if (!messagesContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    return scrollHeight - scrollTop - clientHeight < 10; // 10px threshold
  }, []);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && !userScrolledUp && messagesContainerRef.current) {
      // Only auto-scroll if user is not currently scrolling
      if (!isUserScrolling.current) {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
    }
  }, [messages, autoScroll, userScrolledUp]);

  const scrollToBottom = useCallback((smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      if (smooth) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
      // Re-enable auto-scroll when manually scrolling to bottom
      setUserScrolledUp(false);
    }
  }, []);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    // Mark that user is actively scrolling
    isUserScrolling.current = true;
    
    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    // Set timeout to detect when user stops scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      isUserScrolling.current = false;
    }, 150); // 150ms after user stops scrolling
    
    // Check if user is at bottom
    const atBottom = isAtBottom();
    
    if (atBottom) {
      // User is at bottom, enable auto-scroll
      setUserScrolledUp(false);
    } else {
      // User is not at bottom, disable auto-scroll
      setUserScrolledUp(true);
    }
  }, [isAtBottom]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    userScrolledUp,
    messagesContainerRef,
    scrollToBottom,
    handleScroll
  };
};
