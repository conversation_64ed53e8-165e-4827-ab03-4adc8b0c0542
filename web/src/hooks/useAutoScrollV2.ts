import { useState, useRef, useCallback, useEffect } from 'react';

interface UseAutoScrollOptions {
  autoScroll?: boolean;
  messages?: any[];
}

interface UseAutoScrollReturn {
  userScrolledUp: boolean;
  messagesContainerRef: React.RefObject<HTMLDivElement>;
  scrollToBottom: (smooth?: boolean) => void;
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void;
}

export const useAutoScrollV2 = ({
  autoScroll = true,
  messages = []
}: UseAutoScrollOptions = {}): UseAutoScrollReturn => {
  const [userScrolledUp, setUserScrolledUp] = useState(false);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const isUserScrolling = useRef<boolean>(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageCount = useRef<number>(0);

  // Check if user is at bottom
  const isAtBottom = useCallback(() => {
    if (!messagesContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    return scrollHeight - scrollTop - clientHeight < 10; // 10px threshold
  }, []);

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    console.log('useAutoScrollV2: Messages effect triggered', {
      messagesLength: messages.length,
      lastMessageCount: lastMessageCount.current,
      autoScroll,
      userScrolledUp,
      isUserScrolling: isUserScrolling.current
    });

    // Only auto-scroll if:
    // 1. autoScroll is enabled
    // 2. user hasn't scrolled up
    // 3. user is not currently scrolling
    // 4. we have new messages OR this is the first load
    const shouldAutoScroll = autoScroll && !userScrolledUp && !isUserScrolling.current &&
                            (messages.length > lastMessageCount.current || lastMessageCount.current === 0);

    if (shouldAutoScroll && messagesContainerRef.current) {
      console.log('useAutoScrollV2: Auto scrolling...');
      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        if (messagesContainerRef.current) {
          const oldScrollTop = messagesContainerRef.current.scrollTop;
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
          console.log('useAutoScrollV2: Scrolled from', oldScrollTop, 'to', messagesContainerRef.current.scrollTop);
        }
      });
    }

    // Update message count
    lastMessageCount.current = messages.length;
  }, [messages, autoScroll, userScrolledUp]);

  // Initial scroll to bottom when component mounts
  useEffect(() => {
    if (messagesContainerRef.current && messages.length > 0) {
      requestAnimationFrame(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      });
    }
  }, []); // Only run once on mount

  const scrollToBottom = useCallback((smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      if (smooth) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
      // Re-enable auto-scroll when manually scrolling to bottom
      setUserScrolledUp(false);
    }
  }, []);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    console.log('useAutoScrollV2: User scroll detected');
    // Mark that user is actively scrolling
    isUserScrolling.current = true;

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Set timeout to detect when user stops scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      isUserScrolling.current = false;
      console.log('useAutoScrollV2: User stopped scrolling');
    }, 150); // 150ms after user stops scrolling

    // Check if user is at bottom
    const atBottom = isAtBottom();
    console.log('useAutoScrollV2: At bottom?', atBottom);

    if (atBottom) {
      // User is at bottom, enable auto-scroll
      setUserScrolledUp(false);
      console.log('useAutoScrollV2: Enabled auto-scroll (user at bottom)');
    } else {
      // User is not at bottom, disable auto-scroll
      setUserScrolledUp(true);
      console.log('useAutoScrollV2: Disabled auto-scroll (user scrolled up)');
    }
  }, [isAtBottom]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    userScrolledUp,
    messagesContainerRef,
    scrollToBottom,
    handleScroll
  };
};
