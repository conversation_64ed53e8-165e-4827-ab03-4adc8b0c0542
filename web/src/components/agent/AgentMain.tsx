"use client";

import { useMemo } from "react";
import { useAgentStore } from "@/lib/agent/store";
import { cn } from "@/lib/utils";
import { AgentMessagesBlock } from "./AgentMessagesBlock";
import { ResearchBlock } from "./ResearchBlock";

export default function AgentMain() {
  const openResearchId = useAgentStore((state) => state.openResearchId);
  const doubleColumnMode = useMemo(
    () => openResearchId !== null,
    [openResearchId],
  );

  return (
    <div className="h-full w-full overflow-x-hidden">
      <div className="h-full w-full px-4 pt-4 pb-4 overflow-x-hidden">
        <div className="h-full w-full max-w-4xl mx-auto flex">
          <div className="flex-1 flex flex-col min-w-0">
            <AgentMessagesBlock className="h-full" />
          </div>
          
          {doubleColumnMode && (
            <div className="w-96 pl-8 pb-4 min-w-0">
              <ResearchBlock
                className="h-full w-full"
                researchId={openResearchId}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}