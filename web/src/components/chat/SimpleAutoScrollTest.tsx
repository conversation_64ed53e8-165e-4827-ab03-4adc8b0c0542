'use client';

import React, { useState, useEffect, useRef } from 'react';

interface TestMessage {
  id: string;
  content: string;
  timestamp: number;
}

export const SimpleAutoScrollTest: React.FC = () => {
  const [messages, setMessages] = useState<TestMessage[]>([
    { id: '1', content: '第一条消息', timestamp: Date.now() },
    { id: '2', content: '第二条消息', timestamp: Date.now() + 1000 },
    { id: '3', content: '第三条消息', timestamp: Date.now() + 2000 },
  ]);

  const [userScrolledUp, setUserScrolledUp] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');
  
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const isUserScrolling = useRef<boolean>(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageCount = useRef<number>(3);

  // Check if user is at bottom
  const isAtBottom = () => {
    if (!messagesContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    const atBottom = scrollHeight - scrollTop - clientHeight < 10;
    setDebugInfo(`scrollTop: ${scrollTop}, scrollHeight: ${scrollHeight}, clientHeight: ${clientHeight}, atBottom: ${atBottom}`);
    return atBottom;
  };

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    console.log('Messages changed:', messages.length, 'Last count:', lastMessageCount.current);
    console.log('userScrolledUp:', userScrolledUp, 'isUserScrolling:', isUserScrolling.current);
    
    if (messages.length > lastMessageCount.current && !userScrolledUp && !isUserScrolling.current) {
      console.log('Auto scrolling...');
      if (messagesContainerRef.current) {
        requestAnimationFrame(() => {
          if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
            console.log('Scrolled to:', messagesContainerRef.current.scrollTop);
          }
        });
      }
    }
    
    lastMessageCount.current = messages.length;
  }, [messages, userScrolledUp]);

  // Initial scroll to bottom
  useEffect(() => {
    if (messagesContainerRef.current) {
      requestAnimationFrame(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      });
    }
  }, []);

  // Handle scroll events
  const handleScroll = () => {
    console.log('User scrolling detected');
    isUserScrolling.current = true;
    
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    scrollTimeoutRef.current = setTimeout(() => {
      isUserScrolling.current = false;
      console.log('User stopped scrolling');
    }, 150);
    
    const atBottom = isAtBottom();
    setUserScrolledUp(!atBottom);
    console.log('User scrolled up:', !atBottom);
  };

  // Add new messages
  const addMessage = () => {
    const newMessage = {
      id: Math.random().toString(36).substring(2, 11),
      content: `新消息 ${new Date().toLocaleTimeString()}`,
      timestamp: Date.now()
    };
    console.log('Adding new message:', newMessage.content);
    setMessages(prev => [...prev, newMessage]);
  };

  // Auto add messages
  useEffect(() => {
    const interval = setInterval(addMessage, 3000);
    return () => clearInterval(interval);
  }, []);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
      setUserScrolledUp(false);
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <div className="bg-white border-b p-4">
        <h1 className="text-xl font-semibold">简单自动滚动测试</h1>
        <div className="mt-2 space-y-1 text-sm">
          <div>消息数量: {messages.length}</div>
          <div>用户向上滚动: {userScrolledUp ? '是' : '否'}</div>
          <div>用户正在滚动: {isUserScrolling.current ? '是' : '否'}</div>
          <div className="text-xs text-gray-600">{debugInfo}</div>
        </div>
        <button 
          onClick={addMessage}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          手动添加消息
        </button>
      </div>

      <div className="flex-1 relative">
        <div
          ref={messagesContainerRef}
          className="h-full overflow-y-auto px-4"
          onScroll={handleScroll}
        >
          <div className="space-y-4 py-4">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={`p-4 rounded-lg max-w-3xl ${
                  index % 2 === 0
                    ? "bg-blue-100 ml-auto text-right"
                    : "bg-white border"
                }`}
              >
                <div>{message.content}</div>
                <div className="text-xs text-gray-500 mt-2">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
          <div className="h-32" />
        </div>

        {userScrolledUp && (
          <button
            onClick={scrollToBottom}
            className="fixed bottom-20 right-6 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg"
          >
            ↓
          </button>
        )}
      </div>
    </div>
  );
};
