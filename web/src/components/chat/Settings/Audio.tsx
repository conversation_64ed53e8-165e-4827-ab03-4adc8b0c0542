'use client';

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { useAuthStore, useSettingsStore } from '@/lib/stores';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Tooltip } from '@/components/ui/tooltip';
import { toast } from 'sonner';
import {
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Play,
  Pause,
  Settings as SettingsIcon,
  TestTube,
  Download,
  Upload
} from 'lucide-react';

interface AudioProps {
  settings: any;
  onSettingsChange: (settings: any) => void;
  user: any;
  className?: string;
}

export const Audio: React.FC<AudioProps> = ({
  settings,
  onSettingsChange,
  user,
  className
}) => {
  // TTS Settings
  const [ttsEnabled, setTtsEnabled] = useState(false);
  const [ttsAutoPlay, setTtsAutoPlay] = useState(false);
  const [ttsVoice, setTtsVoice] = useState('');
  const [ttsSpeed, setTtsSpeed] = useState([1]);
  const [ttsPitch, setTtsPitch] = useState([1]);
  const [ttsVolume, setTtsVolume] = useState([1]);
  
  // STT Settings
  const [sttEnabled, setSttEnabled] = useState(false);
  const [sttAutoSend, setSttAutoSend] = useState(false);
  const [sttAutoTranscribe, setSttAutoTranscribe] = useState(true);
  const [sttLanguage, setSttLanguage] = useState('en-US');
  const [sttEngine, setSttEngine] = useState('browser');
  
  // Audio Settings
  const [audioNotifications, setAudioNotifications] = useState(true);
  const [audioNotificationVolume, setAudioNotificationVolume] = useState([0.5]);
  const [customNotificationSound, setCustomNotificationSound] = useState<string | null>(null);
  
  // Available voices
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize from settings
  useEffect(() => {
    if (settings?.audio) {
      const audio = settings.audio;
      setTtsEnabled(audio.ttsEnabled || false);
      setTtsAutoPlay(audio.ttsAutoPlay || false);
      setTtsVoice(audio.ttsVoice || '');
      setTtsSpeed(audio.ttsSpeed ? [audio.ttsSpeed] : [1]);
      setTtsPitch(audio.ttsPitch ? [audio.ttsPitch] : [1]);
      setTtsVolume(audio.ttsVolume ? [audio.ttsVolume] : [1]);
      setSttEnabled(audio.sttEnabled || false);
      setSttAutoSend(audio.sttAutoSend || false);
      setSttAutoTranscribe(audio.sttAutoTranscribe !== false);
      setSttLanguage(audio.sttLanguage || 'en-US');
      setSttEngine(audio.sttEngine || 'browser');
      setAudioNotifications(audio.audioNotifications !== false);
      setAudioNotificationVolume(audio.audioNotificationVolume ? [audio.audioNotificationVolume] : [0.5]);
      setCustomNotificationSound(audio.customNotificationSound || null);
    }
  }, [settings]);

  // Load available voices
  useEffect(() => {
    const loadVoices = () => {
      const voices = speechSynthesis.getVoices();
      setAvailableVoices(voices);
      
      // Set default voice if none selected
      if (!ttsVoice && voices.length > 0) {
        const defaultVoice = voices.find(voice => voice.default) || voices[0];
        setTtsVoice(defaultVoice.name);
      }
    };

    loadVoices();
    speechSynthesis.addEventListener('voiceschanged', loadVoices);

    return () => {
      speechSynthesis.removeEventListener('voiceschanged', loadVoices);
    };
  }, [ttsVoice]);

  const saveSettings = (newSettings: any) => {
    onSettingsChange({
      ...settings,
      audio: {
        ...settings.audio,
        ...newSettings
      }
    });
  };

  const testTTS = () => {
    if (isPlaying) {
      speechSynthesis.cancel();
      setIsPlaying(false);
      return;
    }

    const utterance = new SpeechSynthesisUtterance(
      'This is a test of the text-to-speech functionality. How does it sound?'
    );
    
    const voice = availableVoices.find(v => v.name === ttsVoice);
    if (voice) {
      utterance.voice = voice;
    }
    
    utterance.rate = ttsSpeed[0];
    utterance.pitch = ttsPitch[0];
    utterance.volume = ttsVolume[0];
    
    utterance.onstart = () => setIsPlaying(true);
    utterance.onend = () => setIsPlaying(false);
    utterance.onerror = () => {
      setIsPlaying(false);
      toast.error('TTS test failed');
    };
    
    speechSynthesis.speak(utterance);
  };

  const testSTT = async () => {
    if (isRecording) {
      // Stop recording logic would go here
      setIsRecording(false);
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setIsRecording(true);
      
      // Start recording logic would go here
      toast.success('Recording started. Speak now...');
      
      // Simulate recording for demo
      setTimeout(() => {
        setIsRecording(false);
        stream.getTracks().forEach(track => track.stop());
        toast.success('Recording stopped. Transcription: "This is a test recording."');
      }, 3000);
      
    } catch (error) {
      toast.error('Microphone access denied');
    }
  };

  const playNotificationSound = () => {
    if (customNotificationSound && audioRef.current) {
      audioRef.current.volume = audioNotificationVolume[0];
      audioRef.current.play();
    } else {
      // Play default notification sound
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = audioNotificationVolume[0];
      audio.play().catch(() => {
        // Fallback to system beep
        console.beep?.();
      });
    }
  };

  const handleCustomSoundUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('audio/')) {
      toast.error('Please select an audio file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target?.result as string;
      setCustomNotificationSound(result);
      saveSettings({ customNotificationSound: result });
      toast.success('Custom notification sound uploaded');
    };
    reader.readAsDataURL(file);
  };

  const removeCustomSound = () => {
    setCustomNotificationSound(null);
    saveSettings({ customNotificationSound: null });
  };

  const sttLanguages = [
    { value: 'en-US', label: 'English (US)' },
    { value: 'en-GB', label: 'English (UK)' },
    { value: 'zh-CN', label: '中文 (简体)' },
    { value: 'zh-TW', label: '中文 (繁體)' },
    { value: 'ja-JP', label: '日本語' },
    { value: 'ko-KR', label: '한국어' },
    { value: 'es-ES', label: 'Español' },
    { value: 'fr-FR', label: 'Français' },
    { value: 'de-DE', label: 'Deutsch' },
    { value: 'it-IT', label: 'Italiano' },
    { value: 'pt-BR', label: 'Português (Brasil)' },
    { value: 'ru-RU', label: 'Русский' }
  ];

  const sttEngines = [
    { value: 'browser', label: 'Browser (Web Speech API)' },
    { value: 'openai', label: 'OpenAI Whisper' },
    { value: 'google', label: 'Google Speech-to-Text' }
  ];

  return (
    <div className={cn('space-y-6', className)}>
      {/* Text-to-Speech Settings */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Volume2 className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <Label className="text-base font-medium">Text-to-Speech (TTS)</Label>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="text-sm font-medium">Enable TTS</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Convert text responses to speech
            </div>
          </div>
          <Switch
            checked={ttsEnabled}
            onCheckedChange={(checked) => {
              setTtsEnabled(checked);
              saveSettings({ ttsEnabled: checked });
            }}
          />
        </div>

        {ttsEnabled && (
          <div className="space-y-4 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-sm font-medium">Auto-play Responses</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Automatically read responses aloud
                </div>
              </div>
              <Switch
                checked={ttsAutoPlay}
                onCheckedChange={(checked) => {
                  setTtsAutoPlay(checked);
                  saveSettings({ ttsAutoPlay: checked });
                }}
              />
            </div>

            {/* Voice Selection */}
            <div className="space-y-2">
              <Label>Voice</Label>
              <Select value={ttsVoice} onValueChange={(value) => {
                setTtsVoice(value);
                saveSettings({ ttsVoice: value });
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="Select voice" />
                </SelectTrigger>
                <SelectContent>
                  {availableVoices.map((voice) => (
                    <SelectItem key={voice.name} value={voice.name}>
                      {voice.name} ({voice.lang})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Voice Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Speed: {ttsSpeed[0].toFixed(1)}</Label>
                <Slider
                  value={ttsSpeed}
                  onValueChange={(value) => {
                    setTtsSpeed(value);
                    saveSettings({ ttsSpeed: value[0] });
                  }}
                  min={0.1}
                  max={2}
                  step={0.1}
                />
              </div>

              <div className="space-y-2">
                <Label>Pitch: {ttsPitch[0].toFixed(1)}</Label>
                <Slider
                  value={ttsPitch}
                  onValueChange={(value) => {
                    setTtsPitch(value);
                    saveSettings({ ttsPitch: value[0] });
                  }}
                  min={0}
                  max={2}
                  step={0.1}
                />
              </div>

              <div className="space-y-2">
                <Label>Volume: {Math.round(ttsVolume[0] * 100)}%</Label>
                <Slider
                  value={ttsVolume}
                  onValueChange={(value) => {
                    setTtsVolume(value);
                    saveSettings({ ttsVolume: value[0] });
                  }}
                  min={0}
                  max={1}
                  step={0.1}
                />
              </div>
            </div>

            {/* Test TTS */}
            <Button
              variant="outline"
              onClick={testTTS}
              className="flex items-center gap-2"
            >
              {isPlaying ? (
                <>
                  <Pause className="w-4 h-4" />
                  Stop Test
                </>
              ) : (
                <>
                  <Play className="w-4 h-4" />
                  Test Voice
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      <Separator />

      {/* Speech-to-Text Settings */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Mic className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <Label className="text-base font-medium">Speech-to-Text (STT)</Label>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="text-sm font-medium">Enable STT</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Convert speech to text input
            </div>
          </div>
          <Switch
            checked={sttEnabled}
            onCheckedChange={(checked) => {
              setSttEnabled(checked);
              saveSettings({ sttEnabled: checked });
            }}
          />
        </div>

        {sttEnabled && (
          <div className="space-y-4 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-sm font-medium">Auto-send Messages</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Automatically send after speech recognition
                </div>
              </div>
              <Switch
                checked={sttAutoSend}
                onCheckedChange={(checked) => {
                  setSttAutoSend(checked);
                  saveSettings({ sttAutoSend: checked });
                }}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-sm font-medium">Auto-transcribe</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Automatically transcribe voice recordings
                </div>
              </div>
              <Switch
                checked={sttAutoTranscribe}
                onCheckedChange={(checked) => {
                  setSttAutoTranscribe(checked);
                  saveSettings({ sttAutoTranscribe: checked });
                }}
              />
            </div>

            {/* Language Selection */}
            <div className="space-y-2">
              <Label>Recognition Language</Label>
              <Select value={sttLanguage} onValueChange={(value) => {
                setSttLanguage(value);
                saveSettings({ sttLanguage: value });
              }}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sttLanguages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Engine Selection */}
            <div className="space-y-2">
              <Label>STT Engine</Label>
              <Select value={sttEngine} onValueChange={(value) => {
                setSttEngine(value);
                saveSettings({ sttEngine: value });
              }}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sttEngines.map((engine) => (
                    <SelectItem key={engine.value} value={engine.value}>
                      {engine.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Test STT */}
            <Button
              variant="outline"
              onClick={testSTT}
              className="flex items-center gap-2"
            >
              {isRecording ? (
                <>
                  <MicOff className="w-4 h-4 text-red-500" />
                  Stop Recording
                </>
              ) : (
                <>
                  <Mic className="w-4 h-4" />
                  Test Recording
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      <Separator />

      {/* Audio Notifications */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Volume2 className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <Label className="text-base font-medium">Audio Notifications</Label>
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="text-sm font-medium">Enable Audio Notifications</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Play sound when responses are ready
            </div>
          </div>
          <Switch
            checked={audioNotifications}
            onCheckedChange={(checked) => {
              setAudioNotifications(checked);
              saveSettings({ audioNotifications: checked });
            }}
          />
        </div>

        {audioNotifications && (
          <div className="space-y-4 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
            <div className="space-y-2">
              <Label>Notification Volume: {Math.round(audioNotificationVolume[0] * 100)}%</Label>
              <Slider
                value={audioNotificationVolume}
                onValueChange={(value) => {
                  setAudioNotificationVolume(value);
                  saveSettings({ audioNotificationVolume: value[0] });
                }}
                min={0}
                max={1}
                step={0.1}
              />
            </div>

            {/* Custom Notification Sound */}
            <div className="space-y-2">
              <Label>Custom Notification Sound</Label>
              {customNotificationSound ? (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={playNotificationSound}
                    className="flex items-center gap-2"
                  >
                    <Play className="w-3 h-3" />
                    Test Sound
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={removeCustomSound}
                    className="flex items-center gap-2"
                  >
                    <VolumeX className="w-3 h-3" />
                    Remove
                  </Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={handleCustomSoundUpload}
                  className="flex items-center gap-2"
                >
                  <Upload className="w-4 h-4" />
                  Upload Sound
                </Button>
              )}
            </div>

            <Button
              variant="outline"
              onClick={playNotificationSound}
              className="flex items-center gap-2"
            >
              <TestTube className="w-4 h-4" />
              Test Notification
            </Button>
          </div>
        )}
      </div>

      {/* Hidden audio element for custom sounds */}
      {customNotificationSound && (
        <audio ref={audioRef} src={customNotificationSound} preload="auto" />
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};
