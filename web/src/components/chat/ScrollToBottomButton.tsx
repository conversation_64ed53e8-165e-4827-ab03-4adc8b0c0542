import React from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ScrollToBottomButtonProps {
  visible: boolean;
  onClick: () => void;
  className?: string;
}

export const ScrollToBottomButton: React.FC<ScrollToBottomButtonProps> = ({
  visible,
  onClick,
  className
}) => {
  if (!visible) return null;

  return (
    <button
      onClick={onClick}
      className={cn(
        "fixed bottom-20 right-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-105",
        className
      )}
      title="Scroll to bottom"
    >
      <ChevronDown className="w-5 h-5" />
    </button>
  );
};
