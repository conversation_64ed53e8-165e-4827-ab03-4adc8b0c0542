'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuthStore, useChatStore, useUIStore } from '@/lib/stores';
import { useChats } from '@/hooks/useChats';

import { Messages } from './Messages';
import { MessageInput } from './MessageInput';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { createOpenAITextStream } from '@/lib/api/chat-completion';
import { createNewChat } from '@/lib/api/chats';
import type { Message } from '@/lib/types';

interface ChatProps {
  chatId?: string;
  selectedModels?: string[];
  initialMessages?: Message[];
  onUpdate?: (updates: any) => void;
  className?: string;
}

export const Chat: React.FC<ChatProps> = ({
  chatId,
  selectedModels = [],
  initialMessages = [],
  onUpdate,
  className
}) => {
  const { token } = useAuthStore();
  const { sidebarOpen } = useUIStore();
  const { currentChat, setCurrentChat } = useChatStore();

  const { loadChat, createChat, updateChatData } = useChats();

  const [prompt, setPrompt] = useState('');
  const [files, setFiles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [autoScroll] = useState(true);
  const [chatNotFound, setChatNotFound] = useState(false);
  const [abortControllers, setAbortControllers] = useState<Map<string, AbortController>>(new Map());

  // Load chat when chatId changes
  useEffect(() => {
    if (chatId && chatId !== 'new') {
      console.log('Chat component loading chat:', chatId, 'Current path:', window.location.pathname);
      // Always try to load from server for chat routes
      if (window.location.pathname.startsWith('/c/')) {
        console.log('Loading chat from server:', chatId);
        loadChat(chatId).then((loadedChat) => {
          console.log('Chat loaded successfully:', loadedChat);
        }).catch((error) => {
          console.error('Failed to load chat:', error);
        });
      } else {
        // This is likely a local chat, don't try to load from server
        console.log('Local chat detected, skipping server load:', chatId);
      }
    } else {
      // For non-server chats (home page chats), ensure currentChat is cleared
      // This prevents server chat data from interfering with local chat display
      console.log('Ensuring clean state for local chat');
      if (currentChat && !window.location.pathname.startsWith('/c/')) {
        setCurrentChat(null);
      }
    }
  }, [chatId, loadChat, currentChat, setCurrentChat]);

  // Update messages when current chat changes
  useEffect(() => {
    if (currentChat) {
      console.log('Current chat loaded:', currentChat);
      
      // Extract messages from the nested chat.chat structure
      let chatMessages: Message[] = [];
      
      if (currentChat.chat) {
        // Check if chat.chat has history structure (preferred format)
        if (currentChat.chat.history && currentChat.chat.history.messages) {
          console.log('Using history format messages');
          // Convert history format to flat array
          const historyMessages = currentChat.chat.history.messages;
          const currentId = currentChat.chat.history.currentId;
          
          // Convert object-based messages to array
          chatMessages = Object.values(historyMessages).map((msg: any) => ({
            id: msg.id || Math.random().toString(36).substring(2, 11),
            role: msg.role,
            content: msg.content || '',
            timestamp: msg.timestamp || Math.floor(Date.now() / 1000),
            model: msg.model,

            done: msg.done || (msg.role === 'assistant'),
            error: msg.error || false,
            files: msg.files || []
          })).sort((a, b) => a.timestamp - b.timestamp);
          
        } else if (currentChat.chat.messages) {
          console.log('Using legacy messages format');
          // Handle legacy messages array format
          if (Array.isArray(currentChat.chat.messages)) {
            chatMessages = currentChat.chat.messages;
          } else {
            // Convert object-based messages to array
            chatMessages = Object.values(currentChat.chat.messages).map((msg: any) => ({
              id: msg.id || Math.random().toString(36).substring(2, 11),
              role: msg.role,
              content: msg.content || '',
              timestamp: msg.timestamp || Math.floor(Date.now() / 1000),
              model: msg.model,

              done: msg.done || (msg.role === 'assistant'),
              error: msg.error || false,
              files: msg.files || []
            })).sort((a, b) => a.timestamp - b.timestamp);
          }
        }
      } else if (currentChat.messages) {
        // Fallback to direct messages array (if exists)
        console.log('Using direct messages format');
        chatMessages = currentChat.messages;
      }
      
      console.log('Extracted messages:', chatMessages.length, chatMessages);
      setMessages(chatMessages);
    } else {
      console.log('No current chat, clearing messages');
      setMessages([]);
    }
  }, [currentChat]);

  // Using HTTP API for all chat functionality

  // Handle message submission
  const handleSubmit = useCallback(async (data: {
    prompt: string;
    files?: any[];
    selectedModels: string[];
  }) => {
    if (!data.prompt.trim() && (!data.files || data.files.length === 0)) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if models are selected and not empty
    if (!data.selectedModels.length || data.selectedModels.includes('')) {
      toast.error('Model not selected');
      return;
    }

    if (!token) {
      toast.error('Please sign in to continue');
      return;
    }

    setIsLoading(true);

    try {
      // Create user message
      const userMessage: Message = {
        id: Math.random().toString(36).substring(2, 11),
        role: 'user',
        content: data.prompt,
        timestamp: Math.floor(Date.now() / 1000),
        files: data.files || []
      };

      // Add user message to local state immediately
      const newMessages = [...messages, userMessage];
      setMessages(newMessages);

      // Create or get chat ID first (following original project pattern)
      let currentChatId = chatId;
      console.log('Current chatId:', currentChatId);
      console.log('Current URL path:', window.location.pathname);

      // Check if we need to create a new chat:
      // 1. No chatId or chatId is 'new'
      // 2. We're on the home page (not /c/[chatId] route) - this means it's a local conversation that needs server-side chat
      const needsNewChat = !currentChatId || currentChatId === 'new' || !window.location.pathname.startsWith('/c/');

      if (needsNewChat) {
        console.log('Creating new chat...');
        // Create new chat using original project's structure
        const chatData = {
          id: currentChatId,
          title: data.prompt.slice(0, 50) + (data.prompt.length > 50 ? '...' : ''),
          models: data.selectedModels || selectedModels,
          history: {
            messages: {
              [userMessage.id]: userMessage
            },
            currentId: userMessage.id
          },
          messages: [userMessage],
          tags: [],
          timestamp: Date.now()
        };

        console.log('Calling createNewChat with data:', chatData);
        const newChat = await createNewChat(token, chatData);
        console.log('createNewChat response:', newChat);

        if (newChat?.id) {
          currentChatId = newChat.id;
          console.log('New chat created with ID:', currentChatId);
          // Update URL to match original project behavior
          window.history.replaceState(window.history.state, '', `/c/${currentChatId}`);
        } else {
          console.warn('Failed to create chat, continuing with temporary chat');
          currentChatId = 'local';
        }
      } else {
        console.log('Using existing chat ID:', currentChatId);
      }

      // Generate AI response with streaming
      await generateAIResponse(userMessage, data.selectedModels, newMessages);

      // Clear input
      setPrompt('');
      setFiles([]);

    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
      setIsLoading(false);
    }
  }, [messages, chatId, token, createChat, updateChatData]);

  // Generate AI response with streaming
  const generateAIResponse = useCallback(async (
    userMessage: Message,
    selectedModels: string[],
    currentMessages: Message[]
  ) => {
    try {
      // Process each selected model
      for (const modelId of selectedModels) {
        const responseMessageId = Math.random().toString(36).substring(2, 11);

        // Create assistant message placeholder
        const assistantMessage: Message = {
          id: responseMessageId,
          role: 'assistant',
          content: '',
          timestamp: Math.floor(Date.now() / 1000),
          model: modelId,

        };

        // Add assistant message to state
        setMessages(prev => [...prev, assistantMessage]);

        // Use HTTP API directly (WebSocket functionality disabled for now)
        await generateHTTPResponse(userMessage, modelId, responseMessageId, currentMessages);
      }
    } catch (error) {
      // Check if this was an abort error (user stopped generation)
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('AI response generation stopped by user');
        // Don't show error toast for user-initiated stops
      } else {
        console.error('Failed to generate AI response:', error);
        toast.error('Failed to generate response');
      }
      setIsLoading(false);
    }
  }, [token, setMessages]);

  // Generate response via HTTP API
  const generateHTTPResponse = useCallback(async (
    userMessage: Message,
    modelId: string,
    responseMessageId: string,
    currentMessages: Message[]
  ) => {
    // Create abort controller for this specific response
    const abortController = new AbortController();

    // Add to abort controllers map
    setAbortControllers(prev => new Map(prev.set(responseMessageId, abortController)));

    try {
      // Prepare messages for API (OpenAI format), filtering out tool messages
      const apiMessages = currentMessages
        .filter(msg => msg.role !== 'tool')
        .map(msg => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content
        }));

      // Add current user message
      apiMessages.push({
        role: userMessage.role as 'user' | 'assistant' | 'system',
        content: userMessage.content
      });

      // Use fetch directly with AbortSignal support
      const response = await fetch(`${process.env.NEXT_PUBLIC_WEBUI_API_BASE_URL || 'http://localhost:8080'}/api/chat/completions`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          model: modelId,
          messages: apiMessages,
          stream: true,
          temperature: 0.7,
          max_tokens: 2000
        }),
        signal: abortController.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Process streaming response using createOpenAITextStream
      const textStream = createOpenAITextStream(response.body!, true);

      for await (const update of textStream) {
        if (!update.done && update.value) {
          // Update message content with delta
          setMessages(prev => prev.map(msg =>
            msg.id === responseMessageId
              ? { ...msg, content: msg.content + update.value }
              : msg
          ));
        }

        if (update.done) {
          // Mark message as complete
          setMessages(prev => prev.map(msg =>
            msg.id === responseMessageId
              ? { ...msg, done: true }
              : msg
          ));
          break;
        }

        // Handle any errors in the stream
        // Note: Our TextStreamUpdate doesn't have error property,
        // errors are handled in the catch block
      }

    } catch (error) {
      // Check if this was an abort error
      if (error instanceof Error && error.name === 'AbortError') {
        // This is expected when user stops generation, don't log as error
        console.log('Response generation stopped by user for model', modelId);

        // Mark message as done (stopped)
        setMessages(prev => prev.map(msg =>
          msg.id === responseMessageId
            ? { ...msg, done: true }
            : msg
        ));
      } else {
        // Handle other errors - only log non-abort errors
        console.error('HTTP API error for model', modelId, ':', error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        setMessages(prev => prev.map(msg =>
          msg.id === responseMessageId
            ? { ...msg, content: `Error: ${errorMessage}`, done: true }
            : msg
        ));
      }
    } finally {
      // Clean up abort controller
      setAbortControllers(prev => {
        const newMap = new Map(prev);
        newMap.delete(responseMessageId);
        return newMap;
      });

      // Check if all responses are done and set loading to false
      setMessages(prev => {
        const allDone = prev.every(msg => msg.role !== 'assistant' || msg.done);
        if (allDone) {
          setIsLoading(false);
        }
        return prev;
      });
    }
  }, [token, setMessages]);



  // WebSocket streaming removed - using HTTP streaming only

  // Handle prompt change
  const handlePromptChange = useCallback((newPrompt: string) => {
    setPrompt(newPrompt);
  }, []);

  // Stop response generation
  const stopResponse = useCallback(() => {
    // Cancel all active abort controllers
    abortControllers.forEach((controller) => {
      controller.abort();
    });

    // Clear abort controllers
    setAbortControllers(new Map());

    // Mark all incomplete messages as done
    setMessages(prev => prev.map(msg =>
      msg.role === 'assistant' && !msg.done
        ? { ...msg, done: true }
        : msg
    ));

    // Set loading to false
    setIsLoading(false);
  }, [abortControllers]);

  // Check if any message is currently generating
  const isGenerating = messages.some(msg => msg.role === 'assistant' && !msg.done);



  return (
    <>
      <div className={cn("flex flex-col h-full", className)}>
        {/* Messages area with bottom padding for fixed input when messages exist */}
        <div className={cn(
          "flex-1 overflow-hidden",
          messages.length > 0 ? "pb-24" : ""
        )}>
          <Messages
            chatId={chatId}
            chat={currentChat}
            messages={messages}
            selectedModels={selectedModels}
            prompt={prompt}
            onPromptChange={handlePromptChange}
            onSubmit={handleSubmit}
            autoScroll={autoScroll}
            bottomPadding={messages.length > 0}
            temporaryChatEnabled={false}
          />
        </div>
      </div>

      {/* Fixed Message input area at bottom of viewport - only show when we have messages */}
      {messages.length > 0 && (
        <div className={cn(
          "fixed bottom-0 right-0 z-50 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 transition-all duration-300",
          sidebarOpen ? "lg:left-80" : "left-0"
        )}>
          <div className="max-w-4xl mx-auto p-4">
            <MessageInput
              prompt={prompt}
              onPromptChange={handlePromptChange}
              onSubmit={handleSubmit}
              disabled={isLoading}
              selectedModels={selectedModels}
              files={files}
              onFilesChange={setFiles}
              placeholder="Type your message..."
              isGenerating={isGenerating}
              onStop={stopResponse}
            />
          </div>
        </div>
      )}
    </>
  );
};
