'use client';

import React, { useState, useEffect, useRef, useCallback, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/lib/stores';
import { Spinner } from '@/components/common';
import { ChevronDown } from 'lucide-react';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

interface MessageData {
  id: string;
  parentId: string | null;
  childrenIds: string[];
  role: 'user' | 'assistant';
  content: string;
  files?: any[];
  models?: string[];
  timestamp?: number;
  annotation?: {
    rating?: number;
    [key: string]: any;
  };
}

interface History {
  messages: Record<string, MessageData>;
  currentId: string | null;
}

interface MessagesAdvancedProps {
  chatId?: string;
  history?: History;
  selectedModels?: string[];
  atSelectedModel?: any;
  user?: any;
  prompt?: string;
  readOnly?: boolean;
  bottomPadding?: boolean;
  autoScroll?: boolean;
  processing?: string;
  
  // Event handlers
  sendPrompt?: (history: History, prompt: string, messageId: string) => Promise<void>;
  continueResponse?: (messageId: string) => Promise<void>;
  regenerateResponse?: (messageId: string) => Promise<void>;
  mergeResponses?: (messageId: string, responses: any[]) => Promise<void>;
  chatActionHandler?: (action: string, messageId: string, data?: any) => Promise<void>;
  showMessage?: (messageId: string) => Promise<void>;
  submitMessage?: (messageId: string, content: string) => Promise<void>;
  addMessages?: (messages: MessageData[]) => Promise<void>;
  
  onShowMessage?: (message: MessageData) => void;
  onRegenerateResponse?: (message: MessageData) => void;
  onContinueResponse?: () => void;
  
  className?: string;
}

export const MessagesAdvanced = forwardRef<HTMLDivElement, MessagesAdvancedProps>(({
  chatId = '',
  history = { messages: {}, currentId: null },
  selectedModels = [],
  atSelectedModel,
  user,
  prompt = '',
  readOnly = false,
  bottomPadding = false,
  autoScroll = true,
  processing = '',
  
  sendPrompt,
  continueResponse,
  regenerateResponse,
  mergeResponses,
  chatActionHandler,
  showMessage,
  submitMessage,
  addMessages,
  
  onShowMessage,
  onRegenerateResponse,
  onContinueResponse,
  
  className = 'h-full flex pt-8'
}, ref) => {
  const { token } = useAuthStore();
  const { settings } = useSettingsStore();
  const { 
    mobile, 
    currentChatPage, 
    setCurrentChatPage,
    temporaryChatEnabled 
  } = useUIStore();
  const { setChats } = useChatStore();

  // Component state
  const [messages, setMessages] = useState<MessageData[]>([]);
  const [messagesCount, setMessagesCount] = useState(20);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [userScrolledUp, setUserScrolledUp] = useState(false);

  // Refs
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const lastScrollTop = useRef<number>(0);
  const isScrollingProgrammatically = useRef<boolean>(false);

  // Build messages list from history
  useEffect(() => {
    if (history.currentId) {
      const _messages: MessageData[] = [];
      let message = history.messages[history.currentId];
      
      while (message && _messages.length <= messagesCount) {
        _messages.unshift({ ...message });
        message = message.parentId !== null ? history.messages[message.parentId] : null;
      }
      
      setMessages(_messages);
    } else {
      setMessages([]);
    }
  }, [history, messagesCount]);

  // Auto scroll effect
  useEffect(() => {
    if (autoScroll && bottomPadding && isAutoScrollEnabled && !userScrolledUp) {
      isScrollingProgrammatically.current = true;
      setTimeout(() => {
        scrollToBottom();
      }, 100);
      // Reset the flag after a short delay
      setTimeout(() => {
        isScrollingProgrammatically.current = false;
      }, 500);
    }
  }, [autoScroll, bottomPadding, messages, isAutoScrollEnabled, userScrolledUp]);

  // Scroll to bottom
  const scrollToBottom = useCallback((smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      isScrollingProgrammatically.current = true;
      if (smooth) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
      // Re-enable auto-scroll when manually scrolling to bottom
      setUserScrolledUp(false);
      setIsAutoScrollEnabled(true);
      // Reset the flag after a short delay
      setTimeout(() => {
        isScrollingProgrammatically.current = false;
      }, 500);
    }
  }, []);

  // Load more messages
  const loadMoreMessages = useCallback(async () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop += 100;
    }
    
    setMessagesLoading(true);
    setMessagesCount(prev => prev + 20);
    
    setTimeout(() => {
      setMessagesLoading(false);
    }, 500);
  }, []);

  // Update chat
  const updateChat = useCallback(async () => {
    if (!temporaryChatEnabled && token && chatId) {
      try {
        await updateChatById(token, chatId, {
          history: history,
          messages: messages
        });

        setCurrentChatPage(1);
        const chatsList = await getChatList(token, currentChatPage);
        setChats(chatsList);
      } catch (error) {
        console.error('Failed to update chat:', error);
      }
    }
  }, [temporaryChatEnabled, token, chatId, history, messages, setCurrentChatPage, currentChatPage, setChats]);

  // Navigate to specific message
  const gotoMessage = useCallback(async (message: MessageData, idx: number) => {
    let siblings: string[];
    
    if (message.parentId !== null) {
      siblings = history.messages[message.parentId].childrenIds;
    } else {
      siblings = Object.values(history.messages)
        .filter((msg) => msg.parentId === null)
        .map((msg) => msg.id);
    }

    idx = Math.max(0, Math.min(idx, siblings.length - 1));
    let messageId = siblings[idx];

    if (message.id !== messageId) {
      let messageChildrenIds = history.messages[messageId].childrenIds;
      while (messageChildrenIds.length !== 0) {
        messageId = messageChildrenIds.at(-1)!;
        messageChildrenIds = history.messages[messageId].childrenIds;
      }

      onShowMessage?.(history.messages[messageId]);
    }

    if (settings?.scrollOnBranchChange ?? true) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [history, onShowMessage, settings, scrollToBottom]);

  // Show previous message
  const showPreviousMessage = useCallback(async (message: MessageData) => {
    let messageId: string;
    
    if (message.parentId !== null) {
      const siblings = history.messages[message.parentId].childrenIds;
      const currentIndex = siblings.indexOf(message.id);
      messageId = siblings[Math.max(currentIndex - 1, 0)];
    } else {
      const childrenIds = Object.values(history.messages)
        .filter((msg) => msg.parentId === null)
        .map((msg) => msg.id);
      const currentIndex = childrenIds.indexOf(message.id);
      messageId = childrenIds[Math.max(currentIndex - 1, 0)];
    }

    if (message.id !== messageId) {
      let messageChildrenIds = history.messages[messageId].childrenIds;
      while (messageChildrenIds.length !== 0) {
        messageId = messageChildrenIds.at(-1)!;
        messageChildrenIds = history.messages[messageId].childrenIds;
      }

      onShowMessage?.(history.messages[messageId]);
    }

    if (settings?.scrollOnBranchChange ?? true) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [history, onShowMessage, settings, scrollToBottom]);

  // Show next message
  const showNextMessage = useCallback(async (message: MessageData) => {
    let messageId: string;
    
    if (message.parentId !== null) {
      const siblings = history.messages[message.parentId].childrenIds;
      const currentIndex = siblings.indexOf(message.id);
      messageId = siblings[Math.min(currentIndex + 1, siblings.length - 1)];
    } else {
      const childrenIds = Object.values(history.messages)
        .filter((msg) => msg.parentId === null)
        .map((msg) => msg.id);
      const currentIndex = childrenIds.indexOf(message.id);
      messageId = childrenIds[Math.min(currentIndex + 1, childrenIds.length - 1)];
    }

    if (message.id !== messageId) {
      let messageChildrenIds = history.messages[messageId].childrenIds;
      while (messageChildrenIds.length !== 0) {
        messageId = messageChildrenIds.at(-1)!;
        messageChildrenIds = history.messages[messageId].childrenIds;
      }

      onShowMessage?.(history.messages[messageId]);
    }

    if (settings?.scrollOnBranchChange ?? true) {
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [history, onShowMessage, settings, scrollToBottom]);

  // Rate message
  const rateMessage = useCallback(async (messageId: string, rating: number) => {
    const updatedHistory = {
      ...history,
      messages: {
        ...history.messages,
        [messageId]: {
          ...history.messages[messageId],
          annotation: {
            ...history.messages[messageId].annotation,
            rating: rating
          }
        }
      }
    };

    await updateChat();
  }, [history, updateChat]);

  // Edit message
  const editMessage = useCallback(async (
    messageId: string, 
    { content, files }: { content: string; files?: any[] }, 
    submit = true
  ) => {
    const message = history.messages[messageId];
    
    if (message.role === 'user') {
      if (submit) {
        // Create new user message
        const userMessageId = uuidv4();
        const userMessage: MessageData = {
          id: userMessageId,
          parentId: message.parentId,
          childrenIds: [],
          role: 'user',
          content: content,
          ...(files && { files: files }),
          models: selectedModels,
          timestamp: Math.floor(Date.now() / 1000)
        };

        // Update parent's children
        if (message.parentId !== null) {
          const updatedHistory = {
            ...history,
            messages: {
              ...history.messages,
              [message.parentId]: {
                ...history.messages[message.parentId],
                childrenIds: [...history.messages[message.parentId].childrenIds, userMessageId]
              },
              [userMessageId]: userMessage
            },
            currentId: userMessageId
          };

          if (sendPrompt) {
            await sendPrompt(updatedHistory, content, userMessageId);
          }
        }
      } else {
        // Edit existing user message
        const updatedHistory = {
          ...history,
          messages: {
            ...history.messages,
            [messageId]: {
              ...message,
              content: content,
              files: files
            }
          }
        };
        
        await updateChat();
      }
    } else {
      // Handle assistant message editing
      if (submit) {
        const responseMessageId = uuidv4();
        // Implementation for creating new response
      } else {
        // Edit existing assistant message
        const updatedHistory = {
          ...history,
          messages: {
            ...history.messages,
            [messageId]: {
              ...message,
              content: content
            }
          }
        };
        
        await updateChat();
      }
    }
  }, [history, selectedModels, sendPrompt, updateChat]);

  // Delete message
  const deleteMessage = useCallback(async (messageId: string) => {
    const message = history.messages[messageId];
    
    // Remove from parent's children
    if (message.parentId !== null) {
      const parent = history.messages[message.parentId];
      const updatedChildren = parent.childrenIds.filter(id => id !== messageId);
      
      const updatedHistory = {
        ...history,
        messages: {
          ...history.messages,
          [message.parentId]: {
            ...parent,
            childrenIds: updatedChildren
          }
        }
      };
      
      // Remove the message itself
      delete updatedHistory.messages[messageId];
      
      // Update current ID if needed
      if (history.currentId === messageId) {
        updatedHistory.currentId = message.parentId;
      }
      
      await updateChat();
    }
  }, [history, updateChat]);

  // Action message handler
  const actionMessage = useCallback(async (messageId: string, action: string, data?: any) => {
    if (chatActionHandler) {
      await chatActionHandler(action, messageId, data);
    }
  }, [chatActionHandler]);

  // Save message
  const saveMessage = useCallback(async (messageId: string, content: string) => {
    if (submitMessage) {
      await submitMessage(messageId, content);
    }
  }, [submitMessage]);

  // Trigger scroll
  const triggerScroll = useCallback(() => {
    scrollToBottom();
  }, [scrollToBottom]);

  // Handle scroll to load more messages and detect user scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    const { scrollTop, scrollHeight, clientHeight } = element;

    // Skip if this is a programmatic scroll
    if (isScrollingProgrammatically.current) {
      lastScrollTop.current = scrollTop;
      return;
    }

    // Check if user scrolled up manually
    const isScrollingUp = scrollTop < lastScrollTop.current;
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold

    if (isScrollingUp && !isAtBottom) {
      // User scrolled up manually, disable auto-scroll
      setUserScrolledUp(true);
      setIsAutoScrollEnabled(false);
    } else if (isAtBottom) {
      // User scrolled back to bottom, re-enable auto-scroll
      setUserScrolledUp(false);
      setIsAutoScrollEnabled(true);
    }

    lastScrollTop.current = scrollTop;

    // Load more messages when scrolled to top
    if (element.scrollTop === 0 && messages.at(0)?.parentId !== null && !messagesLoading) {
      loadMoreMessages();
    }
  }, [messages, messagesLoading, loadMoreMessages]);

  return (
    <div className={cn(className, "relative")} ref={ref}>
      <div className="w-full pt-2">
        <div className="w-full">
          {/* Load more messages indicator */}
          {messages.at(0)?.parentId !== null && (
            <div className="w-full flex justify-center py-1 text-xs animate-pulse items-center gap-2">
              {messagesLoading && <Spinner className="size-4" />}
              <div>Loading...</div>
            </div>
          )}

          {/* Messages container */}
          <div
            ref={messagesContainerRef}
            id="messages-container"
            className="overflow-y-auto"
            onScroll={handleScroll}
          >
            {messages.map((message, messageIdx) => (
              <Message
                key={message.id}
                chatId={chatId}
                history={history}
                messageId={message.id}
                idx={messageIdx}
                user={user}
                gotoMessage={gotoMessage}
                showPreviousMessage={showPreviousMessage}
                showNextMessage={showNextMessage}
                updateChat={updateChat}
                editMessage={editMessage}
                deleteMessage={deleteMessage}
                rateMessage={rateMessage}
                actionMessage={actionMessage}
                saveMessage={saveMessage}
                submitMessage={submitMessage}
                regenerateResponse={regenerateResponse}
                continueResponse={continueResponse}
                mergeResponses={mergeResponses}
                addMessages={addMessages}
                triggerScroll={triggerScroll}
                readOnly={readOnly}
              />
            ))}
          </div>

          <div className="pb-12" />
          {bottomPadding && <div className="pb-6" />}
        </div>
      </div>

      {/* Scroll to bottom button - show when user has scrolled up */}
      {userScrolledUp && (
        <button
          onClick={() => scrollToBottom(true)}
          className="fixed bottom-20 right-6 z-50 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-105"
          title="Scroll to bottom"
        >
          <ChevronDown className="w-5 h-5" />
        </button>
      )}
    </div>
  );
});
