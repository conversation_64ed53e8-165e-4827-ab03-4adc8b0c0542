'use client';

import React, { useState, useCallback } from 'react';
import { useAuthStore } from '@/lib/stores';
import { Spinner } from '@/components/common';
import { ChatPlaceholder } from './ChatPlaceholder';
import { ScrollToBottomButton } from './ScrollToBottomButton';
import { useAutoScroll } from '@/hooks/useAutoScroll';
import { cn } from '@/lib/utils';
import type { Message, Chat } from '@/lib/types';

interface MessagesProps {
  chatId?: string;
  chat?: Chat | null;
  messages?: Message[];
  selectedModels: string[];
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onSubmit: (data: any) => void;
  readOnly?: boolean;
  autoScroll?: boolean;
  bottomPadding?: boolean;
  className?: string;
  temporaryChatEnabled?: boolean;
}

export const Messages: React.FC<MessagesProps> = ({
  chatId,
  chat,
  messages = [],
  selectedModels,
  prompt,
  onPromptChange,
  onSubmit,
  readOnly = false,
  autoScroll = true,
  bottomPadding = false,
  className = 'h-full flex pt-8',
  temporaryChatEnabled = false
}) => {
  const { user } = useAuthStore();
  const [messagesCount, setMessagesCount] = useState(20);
  const [messagesLoading, setMessagesLoading] = useState(false);

  // Use the auto-scroll hook
  const {
    userScrolledUp,
    messagesContainerRef,
    bottomRef,
    scrollToBottom,
    handleScroll: autoScrollHandleScroll
  } = useAutoScroll({
    autoScroll,
    bottomPadding,
    messages
  });

  const loadMoreMessages = useCallback(async () => {
    if (messagesLoading) return;

    // Scroll slightly down to disable continuous loading
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop += 100;
    }

    setMessagesLoading(true);
    setMessagesCount(prev => prev + 20);

    // Simulate loading delay
    setTimeout(() => {
      setMessagesLoading(false);
    }, 500);
  }, [messagesLoading]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    // First handle auto-scroll logic
    autoScrollHandleScroll(e);

    // Then handle load more messages
    const element = e.currentTarget;
    if (element.scrollTop === 0 && messages.length >= messagesCount && !messagesLoading) {
      loadMoreMessages();
    }
  }, [autoScrollHandleScroll, messages.length, messagesCount, messagesLoading, loadMoreMessages]);

  // Display messages (limited by messagesCount)
  const displayMessages = messages.slice(-messagesCount);

  // Show placeholder when no messages
  if (!messages.length) {
    // Only show loading state for server chats on /c/ routes
    // For home page chats, always show ChatPlaceholder regardless of chatId
    if (chatId && chatId !== 'new' && window.location.pathname.startsWith('/c/')) {
      return (
        <div className={cn(className, "flex items-center justify-center")}>
          <div className="text-center">
            <Spinner className="size-8 mb-4" />
            <p className="text-gray-500 dark:text-gray-400">Loading conversation...</p>
          </div>
        </div>
      );
    }
    
    // For all other cases (new chats, home page, etc.), show the ChatPlaceholder
    return (
      <div className={cn(className)}>
        <ChatPlaceholder
          selectedModels={selectedModels}
          prompt={prompt}
          onPromptChange={onPromptChange}
          onSubmit={onSubmit}
          temporaryChatEnabled={temporaryChatEnabled}
        />
      </div>
    );
  }

  return (
    <div className={cn(className, "relative")}>
      <div
        ref={messagesContainerRef}
        id="messages-container"
        className="flex-1 overflow-y-auto px-4"
        onScroll={handleScroll}
      >
        {/* Loading indicator for more messages */}
        {messagesLoading && (
          <div className="flex justify-center py-4">
            <Spinner className="size-6" />
          </div>
        )}

        {/* Messages list */}
        <div className="space-y-4 pb-4">
          {displayMessages.map((message, index) => (
            <MessageComponent
              key={message.id || index}
              message={message}
              isLast={index === displayMessages.length - 1}
              readOnly={readOnly}
            />
          ))}
        </div>

        {/* Bottom padding and scroll anchor */}
        {bottomPadding && <div ref={bottomRef} className="h-32" />}
      </div>

      {/* Scroll to bottom button - show when user has scrolled up */}
      <ScrollToBottomButton
        visible={userScrolledUp}
        onClick={() => scrollToBottom(true)}
      />
    </div>
  );
};

// Individual Message Component
interface MessageComponentProps {
  message: Message;
  isLast: boolean;
  readOnly: boolean;
}

const MessageComponent: React.FC<MessageComponentProps> = ({
  message,
  isLast,
  readOnly
}) => {
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  return (
    <div className={cn(
      "flex w-full",
      isUser ? "justify-end" : "justify-start"
    )}>
      <div className={cn(
        "max-w-[80%] rounded-2xl px-4 py-3",
        isUser 
          ? "bg-blue-600 text-white ml-auto" 
          : "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white mr-auto"
      )}>
        {/* Message content */}
        <div className="whitespace-pre-wrap break-words">
          {message.content}
        </div>

        {/* Message metadata */}
        <div className={cn(
          "text-xs mt-2 opacity-70",
          isUser ? "text-blue-100" : "text-gray-500 dark:text-gray-400"
        )}>
          {message.timestamp && new Date(message.timestamp * 1000).toLocaleTimeString()}
          {message.model && ` • ${message.model}`}
        </div>

        {/* File attachments */}
        {message.files && message.files.length > 0 && (
          <div className="mt-2 space-y-1">
            {message.files.map((file, index) => (
              <div
                key={index}
                className={cn(
                  "text-xs px-2 py-1 rounded",
                  isUser 
                    ? "bg-blue-500 text-blue-100" 
                    : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
                )}
              >
                📎 {file.name}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
