'use client';

import React, { useState, useEffect } from 'react';
import { useAutoScrollV3 } from '@/hooks/useAutoScrollV3';
import { ScrollToBottomButton } from './ScrollToBottomButton';

interface TestMessage {
  id: string;
  content: string;
  timestamp: number;
}

export const AutoScrollTestV3: React.FC = () => {
  const [messages, setMessages] = useState<TestMessage[]>([
    { id: '1', content: '欢迎来到自动滚动测试 V3！', timestamp: Date.now() },
    { id: '2', content: '这个版本应该能正常自动滚动', timestamp: Date.now() + 1000 },
    { id: '3', content: '同时支持用户手动滚动中断', timestamp: Date.now() + 2000 },
  ]);

  const [isGenerating, setIsGenerating] = useState(false);
  
  const {
    userScrolledUp,
    messagesContainerRef,
    scrollToBottom,
    handleScroll
  } = useAutoScrollV3({
    autoScroll: true,
    messages
  });

  // Add new messages automatically
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isGenerating) {
        setIsGenerating(true);
        
        // Simulate message generation delay
        setTimeout(() => {
          setMessages(prev => [
            ...prev,
            {
              id: Math.random().toString(36).substring(2, 11),
              content: `自动生成的消息 ${new Date().toLocaleTimeString()} - 这条消息应该自动滚动到底部，除非你向上滚动了。`,
              timestamp: Date.now()
            }
          ]);
          setIsGenerating(false);
        }, 1000);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [isGenerating]);

  const addMessage = () => {
    const newMessage = {
      id: Math.random().toString(36).substring(2, 11),
      content: `手动添加的消息 ${new Date().toLocaleTimeString()}`,
      timestamp: Date.now()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
          自动滚动测试 V3
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          这个版本应该能正常自动滚动，同时支持用户手动滚动中断。
        </p>
        <div className="mt-2 flex items-center space-x-4 text-sm">
          <div className={`px-2 py-1 rounded ${userScrolledUp ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'}`}>
            自动滚动: {userScrolledUp ? '已禁用' : '已启用'}
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            消息数量: {messages.length}
          </div>
        </div>
        <button 
          onClick={addMessage}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
        >
          手动添加消息
        </button>
      </div>

      <div className="flex-1 relative">
        <div
          ref={messagesContainerRef}
          className="h-full overflow-y-auto px-4"
          onScroll={handleScroll}
        >
          <div className="space-y-4 py-4">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={`p-4 rounded-lg max-w-3xl ${
                  index % 2 === 0
                    ? "bg-blue-100 dark:bg-blue-900/20 ml-auto text-right"
                    : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                }`}
              >
                <div className="text-gray-900 dark:text-white">
                  {message.content}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}

            {isGenerating && (
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg max-w-3xl">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-gray-600 dark:text-gray-400">正在生成消息...</span>
                </div>
              </div>
            )}
          </div>

          {/* Bottom padding */}
          <div className="h-32" />
        </div>

        {/* Scroll to bottom button */}
        <ScrollToBottomButton
          visible={userScrolledUp}
          onClick={() => scrollToBottom(true)}
        />
      </div>
    </div>
  );
};
