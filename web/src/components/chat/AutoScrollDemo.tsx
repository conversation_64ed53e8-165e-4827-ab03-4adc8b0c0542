'use client';

import React, { useState, useEffect } from 'react';
import { useAutoScrollV2 } from '@/hooks/useAutoScrollV2';
import { ScrollToBottomButton } from './ScrollToBottomButton';
import { cn } from '@/lib/utils';

interface DemoMessage {
  id: string;
  content: string;
  timestamp: number;
}

export const AutoScrollDemo: React.FC = () => {
  const [messages, setMessages] = useState<DemoMessage[]>([
    { id: '1', content: 'Hello! This is a demo of the auto-scroll functionality.', timestamp: Date.now() },
    { id: '2', content: 'Try scrolling up manually to see how auto-scroll gets disabled.', timestamp: Date.now() + 1000 },
    { id: '3', content: 'New messages will continue to appear automatically.', timestamp: Date.now() + 2000 },
  ]);

  const [isGenerating, setIsGenerating] = useState(false);

  const {
    userScrolledUp,
    messagesContainerRef,
    scrollToBottom,
    handleScroll
  } = useAutoScrollV2({
    autoScroll: true,
    messages
  });

  // Simulate new messages being added
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isGenerating) {
        setIsGenerating(true);
        setTimeout(() => {
          setMessages(prev => [
            ...prev,
            {
              id: Math.random().toString(36).substring(2, 11),
              content: `Auto-generated message at ${new Date().toLocaleTimeString()}. This demonstrates how new messages appear and auto-scroll works.`,
              timestamp: Date.now()
            }
          ]);
          setIsGenerating(false);
        }, 1000);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [isGenerating]);

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
          Auto-Scroll Demo
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Scroll up manually to disable auto-scroll. Scroll back to bottom to re-enable it.
        </p>
        {userScrolledUp && (
          <div className="mt-2 text-sm text-orange-600 dark:text-orange-400">
            ⚠️ Auto-scroll is disabled. Click the button to scroll to bottom.
          </div>
        )}
      </div>

      <div className="flex-1 relative">
        <div
          ref={messagesContainerRef}
          className="h-full overflow-y-auto px-4"
          onScroll={handleScroll}
        >
          <div className="space-y-4 py-4">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={cn(
                  "p-4 rounded-lg max-w-3xl",
                  index % 2 === 0
                    ? "bg-blue-100 dark:bg-blue-900/20 ml-auto text-right"
                    : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                )}
              >
                <div className="text-gray-900 dark:text-white">
                  {message.content}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}

            {isGenerating && (
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg max-w-3xl">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-gray-600 dark:text-gray-400">Generating message...</span>
                </div>
              </div>
            )}
          </div>

          {/* Bottom padding */}
          <div className="h-32" />
        </div>

        {/* Scroll to bottom button */}
        <ScrollToBottomButton
          visible={userScrolledUp}
          onClick={() => scrollToBottom(true)}
        />
      </div>
    </div>
  );
};
