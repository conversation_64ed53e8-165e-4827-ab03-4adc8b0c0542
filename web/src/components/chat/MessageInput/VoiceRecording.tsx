'use client';

import React, { useState, useEffect, useRef } from 'react';
import { cn, blobToFile } from '@/lib/utils';
import { useAuthStore, useSettingsStore } from '@/lib/stores';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { transcribeAudio } from '@/lib/api';
import {
  Mic,
  MicOff,
  Square,
  Play,
  Pause,
  Trash2,
  Check,
  X,
  Volume2
} from 'lucide-react';

interface VoiceRecordingProps {
  onCancel: () => void;
  onConfirm: (data: { text: string; filename?: string }) => void;
  className?: string;
}

export const VoiceRecording: React.FC<VoiceRecordingProps> = ({
  onCancel,
  onConfirm,
  className
}) => {
  const { user } = useAuthStore();
  const { settings } = useSettingsStore();
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [transcribedText, setTranscribedText] = useState('');
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [recordingLevel, setRecordingLevel] = useState(0);
  const [isLoading, setIsLoading] = useState(true); // Add loading state for permission request

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  useEffect(() => {
    startRecording();
    return () => {
      cleanup();
    };
  }, []);

  const cleanup = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
  };

  const startRecording = async () => {
    setIsLoading(true);

    try {
      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia is not supported in this browser');
      }

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      streamRef.current = stream;

      // Set up audio analysis for visual feedback
      const audioContext = new AudioContext();
      const source = audioContext.createMediaStreamSource(stream);
      const analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
      source.connect(analyser);
      analyserRef.current = analyser;

      // Start level monitoring
      monitorAudioLevel();

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm; codecs=opus'
      });

      mediaRecorderRef.current = mediaRecorder;
      const chunks: BlobPart[] = [];

      mediaRecorder.onstart = () => {
        console.log('Recording started');
        setIsLoading(false);
        setIsRecording(true);

        // Start timer
        intervalRef.current = setInterval(() => {
          setRecordingTime(prev => prev + 1);
        }, 1000);
      };

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        console.log('Recording stopped');

        // Use the actual type provided by MediaRecorder, matching original project
        let type = chunks[0]?.type || mediaRecorder.mimeType || 'audio/webm';

        // If not audio, default to audio/webm
        if (!type.startsWith('audio/')) {
          type = 'audio/webm';
        }

        const blob = new Blob(chunks, { type });
        setAudioBlob(blob);
        setAudioUrl(URL.createObjectURL(blob));

        // Auto-transcribe if enabled
        if (settings?.speechAutoTranscribe) {
          transcribeAudioBlob(blob);
        }
      };

      try {
        mediaRecorder.start();
      } catch (error) {
        console.error('Error starting recording:', error);
        toast.error('Error starting recording.');
        setIsLoading(false);
        setIsRecording(false);
        return;
      }

    } catch (error) {
      console.error('Error accessing media devices.', error);

      let errorMessage = 'Error accessing media devices.';
      if (error instanceof Error) {
        if (error.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (error.name === 'NotAllowedError') {
          errorMessage = 'Microphone access denied. Please allow microphone access and try again.';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = 'Microphone not supported in this browser.';
        } else if (error.message.includes('getUserMedia')) {
          errorMessage = 'getUserMedia is not supported in this browser.';
        }
      }

      toast.error(errorMessage);
      setIsLoading(false);
      setIsRecording(false);
    }
  };

  const monitorAudioLevel = () => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const updateLevel = () => {
      analyser.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      setRecordingLevel(Math.min(100, (average / 255) * 100));
      
      if (isRecording) {
        animationFrameRef.current = requestAnimationFrame(updateLevel);
      }
    };

    updateLevel();
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    }
  };

  const playRecording = () => {
    if (audioUrl && audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  const transcribeAudioBlob = async (blob: Blob) => {
    setIsTranscribing(true);
    try {
      if (!user?.token) {
        throw new Error('No authentication token available');
      }

      // Create file with timestamp like original project
      const now = new Date();
      const timestamp = now.toLocaleString();
      const file = blobToFile(blob, `Recording-${timestamp}.webm`);

      // Call the transcription API
      const result = await transcribeAudio(user.token, file).catch((error) => {
        toast.error(`${error}`);
        return null;
      });

      if (result && result.text) {
        console.log(result);
        setTranscribedText(result.text);
      } else if (result === null) {
        // Error already handled by catch block
        return;
      } else {
        throw new Error('No transcription text received');
      }

    } catch (error) {
      console.error('Transcription failed:', error);
      toast.error('Failed to transcribe audio');
    } finally {
      setIsTranscribing(false);
    }
  };

  const handleConfirm = () => {
    if (transcribedText) {
      onConfirm({
        text: transcribedText,
        filename: audioBlob ? `recording-${Date.now()}.webm` : undefined
      });
    } else if (audioBlob) {
      // If no transcription, still allow sending the audio file
      onConfirm({
        text: '[Audio Recording]',
        filename: `recording-${Date.now()}.webm`
      });
    }
  };

  const handleCancel = () => {
    cleanup();
    onCancel();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={cn(
      'fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4',
      className
    )}>
      <div className="bg-white dark:bg-gray-900 rounded-lg p-6 w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            {isLoading ? 'Requesting Microphone Access...' : 'Voice Recording'}
          </h3>
          <div className="text-2xl font-mono text-gray-600 dark:text-gray-400">
            {isLoading ? 'Please allow microphone access' : formatTime(recordingTime)}
          </div>
        </div>

        {/* Recording Visualization */}
        <div className="mb-6">
          {isLoading ? (
            <div className="flex items-center justify-center mb-4">
              <div className="relative">
                <div className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center animate-spin">
                  <Mic className="w-8 h-8 text-white" />
                </div>
              </div>
            </div>
          ) : isRecording ? (
            <div className="flex items-center justify-center mb-4">
              <div className="relative">
                <div className="w-20 h-20 rounded-full bg-red-500 flex items-center justify-center animate-pulse">
                  <Mic className="w-8 h-8 text-white" />
                </div>
                <div
                  className="absolute inset-0 rounded-full border-4 border-red-500 opacity-30"
                  style={{
                    transform: `scale(${1 + recordingLevel / 200})`
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center mb-4">
              <div className="w-20 h-20 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <MicOff className="w-8 h-8 text-gray-500" />
              </div>
            </div>
          )}

          {/* Audio Level */}
          {isRecording && (
            <div className="mb-4">
              <Progress value={recordingLevel} className="h-2" />
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center gap-3 mb-6">
          {isLoading ? (
            <Button
              disabled
              variant="outline"
              size="lg"
              className="flex items-center gap-2"
            >
              <Mic className="w-5 h-5 animate-pulse" />
              Waiting for permission...
            </Button>
          ) : isRecording ? (
            <Button
              onClick={stopRecording}
              variant="outline"
              size="lg"
              className="flex items-center gap-2"
            >
              <Square className="w-5 h-5" />
              Stop
            </Button>
          ) : audioUrl ? (
            <Button
              onClick={playRecording}
              variant="outline"
              size="lg"
              className="flex items-center gap-2"
            >
              {isPlaying ? (
                <>
                  <Pause className="w-5 h-5" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="w-5 h-5" />
                  Play
                </>
              )}
            </Button>
          ) : null}

          {audioUrl && !isRecording && (
            <Button
              onClick={() => transcribeAudioBlob(audioBlob!)}
              variant="outline"
              size="lg"
              disabled={isTranscribing}
              className="flex items-center gap-2"
            >
              {isTranscribing ? (
                <>
                  <div className="w-5 h-5 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                  Transcribing...
                </>
              ) : (
                <>
                  <Volume2 className="w-5 h-5" />
                  Transcribe
                </>
              )}
            </Button>
          )}
        </div>

        {/* Transcription */}
        {transcribedText && (
          <div className="mb-6 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Transcription:
            </div>
            <div className="text-sm text-gray-900 dark:text-gray-100">
              {transcribedText}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between">
          <Button
            onClick={handleCancel}
            variant="outline"
            className="flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel
          </Button>

          <div className="flex items-center gap-2">
            {audioUrl && (
              <Button
                onClick={() => {
                  setAudioBlob(null);
                  setAudioUrl(null);
                  setTranscribedText('');
                  setRecordingTime(0);
                  startRecording();
                }}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                Retry
              </Button>
            )}

            <Button
              onClick={handleConfirm}
              disabled={!transcribedText && !audioBlob}
              className="flex items-center gap-2"
            >
              <Check className="w-4 h-4" />
              Confirm
            </Button>
          </div>
        </div>

        {/* Hidden audio element */}
        {audioUrl && (
          <audio
            ref={audioRef}
            src={audioUrl}
            onEnded={() => setIsPlaying(false)}
            className="hidden"
          />
        )}
      </div>
    </div>
  );
};
