'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ScrollToBottomButton } from './ScrollToBottomButton';

interface TestMessage {
  id: string;
  content: string;
  timestamp: number;
}

export const ScrollInterruptTest: React.FC = () => {
  const [messages, setMessages] = useState<TestMessage[]>([
    { id: '1', content: '欢迎来到滚动中断测试！', timestamp: Date.now() },
    { id: '2', content: '这个测试会每2秒自动添加新消息', timestamp: Date.now() + 1000 },
    { id: '3', content: '尝试向上滚动来查看历史消息', timestamp: Date.now() + 2000 },
    { id: '4', content: '当你向上滚动时，自动滚动应该停止', timestamp: Date.now() + 3000 },
  ]);

  const [userScrolledUp, setUserScrolledUp] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const isUserScrolling = useRef<boolean>(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check if user is at bottom
  const isAtBottom = () => {
    if (!messagesContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    return scrollHeight - scrollTop - clientHeight < 10;
  };

  // Auto scroll to bottom when new messages arrive
  useEffect(() => {
    if (!userScrolledUp && messagesContainerRef.current && !isUserScrolling.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages, userScrolledUp]);

  // Handle scroll events
  const handleScroll = () => {
    // Mark that user is actively scrolling
    isUserScrolling.current = true;
    
    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    
    // Set timeout to detect when user stops scrolling
    scrollTimeoutRef.current = setTimeout(() => {
      isUserScrolling.current = false;
    }, 150);
    
    // Check if user is at bottom
    const atBottom = isAtBottom();
    setUserScrolledUp(!atBottom);
  };

  // Scroll to bottom function
  const scrollToBottom = (smooth: boolean = false) => {
    if (messagesContainerRef.current) {
      if (smooth) {
        messagesContainerRef.current.scrollTo({
          top: messagesContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      } else {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
      setUserScrolledUp(false);
    }
  };

  // Simulate new messages being added
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isGenerating) {
        setIsGenerating(true);
        setTimeout(() => {
          setMessages(prev => [
            ...prev,
            {
              id: Math.random().toString(36).substring(2, 11),
              content: `新消息 ${new Date().toLocaleTimeString()} - 如果你在上面查看历史消息，这条消息不应该让页面自动滚动到底部。`,
              timestamp: Date.now()
            }
          ]);
          setIsGenerating(false);
        }, 1000);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [isGenerating]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
          滚动中断测试
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          向上滚动查看历史消息，自动滚动应该停止。滚动回底部重新启用自动滚动。
        </p>
        <div className="mt-2 flex items-center space-x-4 text-sm">
          <div className={`px-2 py-1 rounded ${userScrolledUp ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'}`}>
            自动滚动: {userScrolledUp ? '已禁用' : '已启用'}
          </div>
          <div className={`px-2 py-1 rounded ${isUserScrolling.current ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
            用户滚动: {isUserScrolling.current ? '进行中' : '停止'}
          </div>
        </div>
      </div>

      <div className="flex-1 relative">
        <div
          ref={messagesContainerRef}
          className="h-full overflow-y-auto px-4"
          onScroll={handleScroll}
        >
          <div className="space-y-4 py-4">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={`p-4 rounded-lg max-w-3xl ${
                  index % 2 === 0
                    ? "bg-blue-100 dark:bg-blue-900/20 ml-auto text-right"
                    : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                }`}
              >
                <div className="text-gray-900 dark:text-white">
                  {message.content}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}

            {isGenerating && (
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg max-w-3xl">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-gray-600 dark:text-gray-400">正在生成消息...</span>
                </div>
              </div>
            )}
          </div>

          {/* Bottom padding */}
          <div className="h-32" />
        </div>

        {/* Scroll to bottom button */}
        <ScrollToBottomButton
          visible={userScrolledUp}
          onClick={() => scrollToBottom(true)}
        />
      </div>
    </div>
  );
};
