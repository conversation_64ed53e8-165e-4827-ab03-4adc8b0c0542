# 自动滚动功能实现

## 功能概述

实现了智能的自动滚动功能，当有新消息时会自动滚动到底部，但当用户手动向上滚动时会停止自动滚动，让用户可以查看历史消息。当用户滚动回底部时，自动滚动功能会重新启用。

## 核心特性

1. **智能检测用户滚动行为**：区分程序化滚动和用户手动滚动
2. **自动滚动控制**：新消息到达时自动滚动到底部
3. **用户友好的交互**：用户向上滚动时停止自动滚动
4. **滚动到底部按钮**：当用户向上滚动时显示快速返回底部的按钮
5. **平滑滚动动画**：提供更好的用户体验

## 文件结构

### 核心文件

- `useAutoScroll.ts` - 自动滚动逻辑的 React Hook
- `ScrollToBottomButton.tsx` - 滚动到底部按钮组件
- `Messages.tsx` - 更新后的消息组件，集成了自动滚动功能
- `MessagesAdvanced.tsx` - 高级消息组件，也集成了自动滚动功能
- `AutoScrollDemo.tsx` - 演示组件，展示自动滚动功能

### 演示页面

- `app/demo/auto-scroll/page.tsx` - 演示页面路由

## 使用方法

### 1. 使用 useAutoScroll Hook

```typescript
import { useAutoScroll } from '@/hooks/useAutoScroll';

const {
  userScrolledUp,
  messagesContainerRef,
  bottomRef,
  scrollToBottom,
  handleScroll
} = useAutoScroll({
  autoScroll: true,
  bottomPadding: true,
  messages
});
```

### 2. 在组件中应用

```tsx
<div
  ref={messagesContainerRef}
  className="overflow-y-auto"
  onScroll={handleScroll}
>
  {/* 消息内容 */}
  <div ref={bottomRef} className="h-32" />
</div>

<ScrollToBottomButton
  visible={userScrolledUp}
  onClick={() => scrollToBottom(true)}
/>
```

## 技术实现

### 滚动检测逻辑

1. **程序化滚动标记**：使用 `isScrollingProgrammatically` ref 来标记程序触发的滚动
2. **滚动方向检测**：比较当前滚动位置与上次位置来判断滚动方向
3. **底部检测**：使用 50px 阈值来判断是否接近底部
4. **状态管理**：使用 `userScrolledUp` 和 `isAutoScrollEnabled` 状态控制行为

### 关键参数

- `scrollThreshold: 50px` - 判断是否在底部的阈值
- `programmaticScrollDelay: 500ms` - 程序化滚动标记的重置延迟
- `autoScrollDelay: 100ms` - 自动滚动的延迟时间

## 测试

访问 `/demo/auto-scroll` 页面可以测试自动滚动功能：

1. 页面会自动生成新消息
2. 尝试向上滚动查看历史消息
3. 观察自动滚动如何被禁用
4. 点击"滚动到底部"按钮或手动滚动到底部
5. 观察自动滚动如何重新启用

## 集成到现有组件

现有的 `Messages.tsx` 和 `MessagesAdvanced.tsx` 组件已经集成了这个功能。如果需要在其他组件中使用，只需：

1. 导入 `useAutoScroll` hook
2. 导入 `ScrollToBottomButton` 组件
3. 按照上述使用方法进行集成

## 浏览器兼容性

- 支持所有现代浏览器
- 使用标准的 DOM API
- 平滑滚动使用 `scrollTo({ behavior: 'smooth' })`
- 降级方案使用 `scrollTop` 直接设置
