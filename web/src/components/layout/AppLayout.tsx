'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUIStore, useAuthStore, useAppStore, useChatStore } from '@/lib/stores';
import { useAuth } from '@/hooks/useAuth';
import { Navbar } from './Navbar';
import { Sidebar } from './Sidebar';
import { WebSocketProvider } from '@/components/websocket/WebSocketProvider';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children, className }) => {
  const router = useRouter();
  const { sidebarOpen } = useUIStore();
  const { isAuthenticated, isLoading: authLoading, token } = useAuthStore();
  const { isInitialized, initialize, models, refreshModels, selectedModels, setSelectedModels } = useAppStore();
  const { setCurrentChat } = useChatStore();
  const { refreshSession } = useAuth();
  const [currentConversationId, setCurrentConversationId] = useState<string | undefined>(undefined);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + O for new chat (matching ifm-chat-feature-k2)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'O') {
        event.preventDefault();
        handleNewConversation('chat');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [router]);

  // Initialize app on mount
  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [isInitialized, initialize]);

  // Refresh session and models if authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading && token) {
      // Try to refresh session, but don't block the app if it fails
      const currentUser = useAuthStore.getState().user;
      if (!currentUser) {
        refreshSession().catch((error) => {
          console.warn('Session refresh failed during app initialization:', error);
          // Create a minimal user object if session refresh fails
          // This allows the app to continue working even if the session API is not available
          const minimalUser = {
            id: 'unknown',
            email: '<EMAIL>',
            name: 'User',
            role: 'user',
            profile_image_url: null
          };
          useAuthStore.getState().setUser(minimalUser);
        });
      }
      
      // Refresh models with auth token
      refreshModels(token).catch((error) => {
        console.warn('Models refresh failed during app initialization:', error);
        // Don't block the app if models refresh fails
      });
    }
  }, [isAuthenticated, authLoading, token, refreshSession, refreshModels]);

  // Handle new conversation creation
  const handleNewConversation = (type?: 'chat' | 'agent') => {
    console.log('Creating new conversation:', type);
    // Clear current chat state to reset the interface
    setCurrentChat(null);
    setCurrentConversationId(undefined);
    router.push('/');
  };

  // Handle conversation selection
  const handleConversationSelect = (id: string) => {
    console.log('Selecting conversation:', id);
    setCurrentConversationId(id);
    router.push(`/c/${id}`);
  };

  // Redirect to auth if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth');
    }
  }, [isAuthenticated, authLoading, router]);

  // Show loading screen while initializing
  if (!isInitialized || authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Only redirect to auth if we have no token at all
  // This matches the original Svelte behavior where the app renders first
  // and token validation happens in the background
  if (!isAuthenticated && !authLoading && typeof window !== 'undefined') {
    const storedToken = localStorage.getItem('token');
    if (!storedToken) {
      // No token at all, redirect to auth
      window.location.href = '/auth';
      return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Redirecting to sign in...</p>
          </div>
        </div>
      );
    }
    // If we have a token, render the app and let auth validation happen in background
    // This prevents interrupting ongoing chats during token validation
  }

  return (
    <WebSocketProvider>
      <div className={cn("min-h-screen bg-gray-50 dark:bg-gray-900 overflow-x-hidden", className)}>
        {/* Sidebar */}
        <Sidebar 
          currentConversationId={currentConversationId}
          onConversationSelect={handleConversationSelect}
          onNewConversation={handleNewConversation}
        />

        {/* Sidebar overlay for mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-opacity-50 z-40 lg:hidden"
            onClick={() => useUIStore.getState().setSidebarOpen(false)}
          />
        )}

        {/* Main content area */}
        <div className={cn(
          "flex flex-col min-h-screen transition-all duration-300 ease-in-out",
          sidebarOpen ? "lg:ml-80" : "ml-0"
        )}>
          {/* Navigation bar */}
          <Navbar
            selectedModels={selectedModels}
            onModelsChange={setSelectedModels}
            initNewChat={() => handleNewConversation('chat')}
          />

          {/* Main content */}
          <main className="flex-1 overflow-hidden">
            <div className="h-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </WebSocketProvider>
  );
};
